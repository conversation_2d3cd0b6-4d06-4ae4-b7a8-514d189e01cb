error[E0308]: mismatched types
  --> tests/ui/invalid-copy-attribute.rs:11:14
   |
11 |         type IncorrectCopySize;
   |              ^^^^^^^^^^^^^^^^^
   |              |
   |              expected an array with a size of 9, found one with a size of 10
   |              expected due to this

error[E0512]: cannot transmute between types of different sizes, or dependently-sized types
 --> tests/ui/invalid-copy-attribute.rs:4:1
  |
4 | #[swift_bridge::bridge]
  | ^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: source type: `__swift_bridge__IncorrectCopySize` (80 bits)
  = note: target type: `IncorrectCopySize` (72 bits)
  = note: this error originates in the attribute macro `swift_bridge::bridge` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0512]: cannot transmute between types of different sizes, or dependently-sized types
 --> tests/ui/invalid-copy-attribute.rs:4:1
  |
4 | #[swift_bridge::bridge]
  | ^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: source type: `IncorrectCopySize` (72 bits)
  = note: target type: `__swift_bridge__IncorrectCopySize` (80 bits)
  = note: this error originates in the attribute macro `swift_bridge::bridge` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `DoesNotImplementCopy: Copy` is not satisfied
 --> tests/ui/invalid-copy-attribute.rs:8:14
  |
8 |         type DoesNotImplementCopy;
  |              ^^^^^^^^^^^^^^^^^^^^ the trait `Copy` is not implemented for `DoesNotImplementCopy`
  |
note: required by a bound in `assert_copy`
 --> $WORKSPACE/src/copy_support.rs
  |
  | pub fn assert_copy<T: Copy>() {}
  |                       ^^^^ required by this bound in `assert_copy`
help: consider annotating `DoesNotImplementCopy` with `#[derive(Copy)]`
  |
15+ #[derive(Copy)]
16| pub struct DoesNotImplementCopy(u8);
  |
