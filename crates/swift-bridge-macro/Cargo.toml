[package]
name = "swift-bridge-macro"
version = "0.1.57"
edition = "2021"
keywords = ["swift", "ffi", "bindings", "ios", "mac"]
description = "Powers swift-bridge module code generation."
repository = "https://github.com/chinedufn/swift-bridge"
license = "Apache-2.0/MIT"

[lib]
proc-macro = true

[dependencies]
proc-macro2 = "1"
quote = "1"
syn = { version = "1", features = ["full"] }
swift-bridge-ir = {version = "0.1.57", path = "../swift-bridge-ir"}

[dev-dependencies]
swift-bridge = {path = "../../"}
trybuild = "1.0"
