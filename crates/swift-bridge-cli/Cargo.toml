[package]
name = "swift-bridge-cli"
version = "0.1.57"
edition = "2021"
keywords = ["swift", "ffi", "bindings", "ios", "mac"]
description = "Parse Rust files for swift-bridge modules and generate the corresponding Swift and C code for them."
repository = "https://github.com/chinedufn/swift-bridge"
license = "Apache-2.0/MIT"

[dependencies]
clap = "3"
swift-bridge-build = { version = "0.1.57", path = "../swift-bridge-build" }
