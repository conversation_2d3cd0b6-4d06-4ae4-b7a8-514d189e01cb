// TODO:
// - Define struct SliceTestOpaqueRustType
// - Add a method to create Vec<SliceTestOpaqueRustType>
// - Add method to reflect &[OpaqueRustType]
//   - Add test to verify that we can iterate over the slice
//   - Add test to verify that that we can index into the slice
//   - Add test to verify that we can get the length of the slice
// - Create SliceTests.swift
//   - Add Swift test verifying that we can use an Array<SliceTestOpaqueRustType> as a slice
//   - Add Swift test verifying that we can use a RustVec<SliceTestOpaqueRustType> as a slice
