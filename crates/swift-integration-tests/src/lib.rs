mod expose_opaque_rust_type;
mod import_opaque_swift_class;

mod async_function;
mod boxed_functions;
mod conditional_compilation;
mod generics;
mod option;
mod pointer;
mod primitive;
mod result;
mod rust_function_uses_opaque_swift_type;
mod shared_types;
mod single_representation_type_elision;
mod slice;
mod string;
mod swift_function_uses_opaque_rust_type;
mod swift_function_uses_opaque_swift_type;
mod tuple;
mod vec;

mod enum_attributes;
mod function_attributes;
mod opaque_type_attributes;
mod sendable_attribute;
mod struct_attributes;

mod futures_experiment;

mod argument_attributes;
