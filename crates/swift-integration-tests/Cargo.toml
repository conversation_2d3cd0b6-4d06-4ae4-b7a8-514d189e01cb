[package]
name = "swift-integration-tests"
version = "0.1.0"
edition = "2021"
publish = []

build = "build.rs"

[features]
# Feature flags are for our conditional compilation tests.
default = ["this_is_enabled"]
this_is_enabled = []
this_is_not_enabled = []

[lib]
crate-type = ["staticlib"]

[build-dependencies]
swift-bridge-build = {path = "../swift-bridge-build"}

[dependencies]
swift-bridge = {path = "../../", features = ["async"]}
