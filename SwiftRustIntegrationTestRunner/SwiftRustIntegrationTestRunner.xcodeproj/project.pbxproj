// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		1745111529BE189B00B96A1A /* TupleTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1745111429BE189B00B96A1A /* TupleTests.swift */; };
		1784BE2829CE86D600AE5A4A /* Tuple.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1784BE2729CE86D600AE5A4A /* Tuple.swift */; };
		178F1CD3298E97FB00335AA0 /* ArgumentAttributesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 178F1CD2298E97FB00335AA0 /* ArgumentAttributesTest.swift */; };
		2202BC0827B2DD1700D43CC4 /* SharedEnumTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2202BC0727B2DD1700D43CC4 /* SharedEnumTests.swift */; };
		22043293274A8FDF00BAE645 /* VecTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22043292274A8FDF00BAE645 /* VecTests.swift */; };
		22043295274ADA7A00BAE645 /* OptionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22043294274ADA7A00BAE645 /* OptionTests.swift */; };
		22043297274B0AB000BAE645 /* Option.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22043296274B0AB000BAE645 /* Option.swift */; };
		220432A7274C953E00BAE645 /* PointerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220432A6274C953E00BAE645 /* PointerTests.swift */; };
		220432A9274D31DC00BAE645 /* Pointer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220432A8274D31DC00BAE645 /* Pointer.swift */; };
		220432AF274E7BF800BAE645 /* SharedStructTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220432AE274E7BF800BAE645 /* SharedStructTests.swift */; };
		220432EA2753092C00BAE645 /* RustFnUsesOpaqueSwiftType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220432E92753092C00BAE645 /* RustFnUsesOpaqueSwiftType.swift */; };
		220432EC27530AFC00BAE645 /* RustFnUsesOpaqueSwiftTypeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220432EB27530AFC00BAE645 /* RustFnUsesOpaqueSwiftTypeTests.swift */; };
		22046383282B4E3F00A09119 /* FunctionAttributeGetTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22046382282B4E3F00A09119 /* FunctionAttributeGetTests.swift */; };
		221E16B42786233600F94AC0 /* ConditionalCompilationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 221E16B32786233600F94AC0 /* ConditionalCompilationTests.swift */; };
		221E16B62786F9FF00F94AC0 /* AlreadyDeclaredAttributeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 221E16B52786F9FF00F94AC0 /* AlreadyDeclaredAttributeTests.swift */; };
		222A81E928EB5BB100D4A412 /* Primitive.swift in Sources */ = {isa = PBXBuildFile; fileRef = 222A81E828EB5BB100D4A412 /* Primitive.swift */; };
		222A81EB28EB5DF800D4A412 /* PrimitiveTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 222A81EA28EB5DF800D4A412 /* PrimitiveTests.swift */; };
		22553324281DB5FC008A3121 /* GenericTests.rs.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22553323281DB5FC008A3121 /* GenericTests.rs.swift */; };
		225908FC28DA0E320080C737 /* ResultTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225908FB28DA0E320080C737 /* ResultTests.swift */; };
		225908FE28DA0F9F0080C737 /* Result.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225908FD28DA0F9F0080C737 /* Result.swift */; };
		226F944B27BF79B400243D86 /* String.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226F944A27BF79B400243D86 /* String.swift */; };
		2289E82C29A879A7009D89D7 /* SingleRepresentationTypeElisionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2289E82B29A879A7009D89D7 /* SingleRepresentationTypeElisionTests.swift */; };
		228FE5D52740DB6A00805D9E /* SwiftRustIntegrationTestRunnerApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE5D42740DB6A00805D9E /* SwiftRustIntegrationTestRunnerApp.swift */; };
		228FE5D72740DB6A00805D9E /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE5D62740DB6A00805D9E /* ContentView.swift */; };
		228FE5D92740DB6D00805D9E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 228FE5D82740DB6D00805D9E /* Assets.xcassets */; };
		228FE5DC2740DB6D00805D9E /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 228FE5DB2740DB6D00805D9E /* Preview Assets.xcassets */; };
		228FE5E72740DB6D00805D9E /* StringTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE5E62740DB6D00805D9E /* StringTests.swift */; };
		228FE6062740DCA100805D9E /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 228FE6052740DC9A00805D9E /* libresolv.tbd */; platformFilter = maccatalyst; };
		228FE60C2740F42000805D9E /* ASwiftStack.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE60B2740F42000805D9E /* ASwiftStack.swift */; };
		228FE60E2740F93D00805D9E /* libswift_integration_tests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 228FE6032740DC7900805D9E /* libswift_integration_tests.a */; };
		228FE61027416C0300805D9E /* OpaqueRustStructTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE60F27416C0300805D9E /* OpaqueRustStructTests.swift */; };
		228FE61227428A8D00805D9E /* OpaqueSwiftStructTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE61127428A8D00805D9E /* OpaqueSwiftStructTests.swift */; };
		228FE64627480E1D00805D9E /* SwiftBridgeCore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE64427480E1C00805D9E /* SwiftBridgeCore.swift */; };
		228FE64A274919C600805D9E /* swift-integration-tests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 228FE648274919C500805D9E /* swift-integration-tests.swift */; };
		22BC10F62799283100A0D046 /* SharedStruct.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22BC10F52799283100A0D046 /* SharedStruct.swift */; };
		22BC10F82799A3A000A0D046 /* SharedStructAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22BC10F72799A3A000A0D046 /* SharedStructAttributes.swift */; };
		22BC4BBA294B8CCD0032B8A8 /* SharedEnumAttributeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22BC4BB9294B8CCD0032B8A8 /* SharedEnumAttributeTests.swift */; };
		22BC4BBC294BA0EC0032B8A8 /* SharedEnumAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22BC4BBB294BA0EC0032B8A8 /* SharedEnumAttributes.swift */; };
		22BCAAB927A2607700686A21 /* FunctionAttributeIdentifiableTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22BCAAB827A2607700686A21 /* FunctionAttributeIdentifiableTests.swift */; };
		22C0625328CE699D007A6F67 /* Callbacks.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22C0625228CE699D007A6F67 /* Callbacks.swift */; };
		22C0625528CE6C9A007A6F67 /* CallbackTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22C0625428CE6C9A007A6F67 /* CallbackTests.swift */; };
		22C0AD51278ECA9E00A96469 /* SharedStructAttributeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22C0AD50278ECA9E00A96469 /* SharedStructAttributeTests.swift */; };
		22D092A327B7E865009A4C2B /* AsyncTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22D092A227B7E865009A4C2B /* AsyncTests.swift */; };
		22EE4E0928B5388000FEC83C /* SwiftFnUsesOpaqueSwiftType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22EE4E0828B5388000FEC83C /* SwiftFnUsesOpaqueSwiftType.swift */; };
		22EE4E0B28B538A700FEC83C /* SwiftFnUsesOpaqueSwiftTypeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22EE4E0A28B538A700FEC83C /* SwiftFnUsesOpaqueSwiftTypeTests.swift */; };
		22F7CF2A2A42EA7800517966 /* Vec.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F7CF292A42EA7800517966 /* Vec.swift */; };
		22FD1C542753CB2A00F64281 /* SwiftFnUsesOpaqueRustType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FD1C532753CB2A00F64281 /* SwiftFnUsesOpaqueRustType.swift */; };
		22FD1C562753CB3F00F64281 /* SwiftFnUsesOpaqueRustTypeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FD1C552753CB3F00F64281 /* SwiftFnUsesOpaqueRustTypeTests.swift */; };
		C926E4DE294F07AA0027E7E2 /* FunctionAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = C926E4DD294F07AA0027E7E2 /* FunctionAttributes.swift */; };
		C926E4E0294F18C50027E7E2 /* FunctionAttributeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = C926E4DF294F18C50027E7E2 /* FunctionAttributeTests.swift */; };
		DE9444DC2D5241AD007A83A4 /* SendableAttributeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE9444DB2D5241A8007A83A4 /* SendableAttributeTests.swift */; };
		DE9444DE2D527C3B007A83A4 /* SendableAttribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE9444DD2D527C31007A83A4 /* SendableAttribute.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		228FE5E32740DB6D00805D9E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 228FE5C92740DB6A00805D9E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 228FE5D02740DB6A00805D9E;
			remoteInfo = SwiftRustIntegrationTestRunner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1745111429BE189B00B96A1A /* TupleTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TupleTests.swift; sourceTree = "<group>"; };
		1784BE2729CE86D600AE5A4A /* Tuple.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Tuple.swift; sourceTree = "<group>"; };
		178F1CD2298E97FB00335AA0 /* ArgumentAttributesTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArgumentAttributesTest.swift; sourceTree = "<group>"; };
		2202BC0727B2DD1700D43CC4 /* SharedEnumTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedEnumTests.swift; sourceTree = "<group>"; };
		22043292274A8FDF00BAE645 /* VecTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VecTests.swift; sourceTree = "<group>"; };
		22043294274ADA7A00BAE645 /* OptionTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptionTests.swift; sourceTree = "<group>"; };
		22043296274B0AB000BAE645 /* Option.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Option.swift; sourceTree = "<group>"; };
		220432A6274C953E00BAE645 /* PointerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointerTests.swift; sourceTree = "<group>"; };
		220432A8274D31DC00BAE645 /* Pointer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Pointer.swift; sourceTree = "<group>"; };
		220432AE274E7BF800BAE645 /* SharedStructTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedStructTests.swift; sourceTree = "<group>"; };
		220432E92753092C00BAE645 /* RustFnUsesOpaqueSwiftType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RustFnUsesOpaqueSwiftType.swift; sourceTree = "<group>"; };
		220432EB27530AFC00BAE645 /* RustFnUsesOpaqueSwiftTypeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RustFnUsesOpaqueSwiftTypeTests.swift; sourceTree = "<group>"; };
		22046382282B4E3F00A09119 /* FunctionAttributeGetTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FunctionAttributeGetTests.swift; sourceTree = "<group>"; };
		221E16B32786233600F94AC0 /* ConditionalCompilationTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConditionalCompilationTests.swift; sourceTree = "<group>"; };
		221E16B52786F9FF00F94AC0 /* AlreadyDeclaredAttributeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlreadyDeclaredAttributeTests.swift; sourceTree = "<group>"; };
		222A81E828EB5BB100D4A412 /* Primitive.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Primitive.swift; sourceTree = "<group>"; };
		222A81EA28EB5DF800D4A412 /* PrimitiveTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PrimitiveTests.swift; sourceTree = "<group>"; };
		22553323281DB5FC008A3121 /* GenericTests.rs.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenericTests.rs.swift; sourceTree = "<group>"; };
		225908FB28DA0E320080C737 /* ResultTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultTests.swift; sourceTree = "<group>"; };
		225908FD28DA0F9F0080C737 /* Result.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Result.swift; sourceTree = "<group>"; };
		226F944A27BF79B400243D86 /* String.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = String.swift; sourceTree = "<group>"; };
		2289E82B29A879A7009D89D7 /* SingleRepresentationTypeElisionTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SingleRepresentationTypeElisionTests.swift; sourceTree = "<group>"; };
		228FE5D12740DB6A00805D9E /* SwiftRustIntegrationTestRunner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftRustIntegrationTestRunner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		228FE5D42740DB6A00805D9E /* SwiftRustIntegrationTestRunnerApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftRustIntegrationTestRunnerApp.swift; sourceTree = "<group>"; };
		228FE5D62740DB6A00805D9E /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		228FE5D82740DB6D00805D9E /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		228FE5DB2740DB6D00805D9E /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		228FE5DD2740DB6D00805D9E /* SwiftRustIntegrationTestRunner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SwiftRustIntegrationTestRunner.entitlements; sourceTree = "<group>"; };
		228FE5E22740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SwiftRustIntegrationTestRunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		228FE5E62740DB6D00805D9E /* StringTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringTests.swift; sourceTree = "<group>"; };
		228FE6012740DC2000805D9E /* BridgingHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BridgingHeader.h; sourceTree = "<group>"; };
		228FE6032740DC7900805D9E /* libswift_integration_tests.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libswift_integration_tests.a; path = ../target/debug/libswift_integration_tests.a; sourceTree = "<group>"; };
		228FE6052740DC9A00805D9E /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		228FE60B2740F42000805D9E /* ASwiftStack.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ASwiftStack.swift; sourceTree = "<group>"; };
		228FE60F27416C0300805D9E /* OpaqueRustStructTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpaqueRustStructTests.swift; sourceTree = "<group>"; };
		228FE61127428A8D00805D9E /* OpaqueSwiftStructTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpaqueSwiftStructTests.swift; sourceTree = "<group>"; };
		228FE64427480E1C00805D9E /* SwiftBridgeCore.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SwiftBridgeCore.swift; sourceTree = "<group>"; };
		228FE6472749127400805D9E /* SwiftBridgeCore.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SwiftBridgeCore.h; sourceTree = "<group>"; };
		228FE648274919C500805D9E /* swift-integration-tests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = "swift-integration-tests.swift"; path = "swift-integration-tests/swift-integration-tests.swift"; sourceTree = "<group>"; };
		228FE649274919C600805D9E /* swift-integration-tests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "swift-integration-tests.h"; path = "swift-integration-tests/swift-integration-tests.h"; sourceTree = "<group>"; };
		22BC10F52799283100A0D046 /* SharedStruct.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedStruct.swift; sourceTree = "<group>"; };
		22BC10F72799A3A000A0D046 /* SharedStructAttributes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedStructAttributes.swift; sourceTree = "<group>"; };
		22BC4BB9294B8CCD0032B8A8 /* SharedEnumAttributeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedEnumAttributeTests.swift; sourceTree = "<group>"; };
		22BC4BBB294BA0EC0032B8A8 /* SharedEnumAttributes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedEnumAttributes.swift; sourceTree = "<group>"; };
		22BCAAB827A2607700686A21 /* FunctionAttributeIdentifiableTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FunctionAttributeIdentifiableTests.swift; sourceTree = "<group>"; };
		22C0625228CE699D007A6F67 /* Callbacks.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Callbacks.swift; sourceTree = "<group>"; };
		22C0625428CE6C9A007A6F67 /* CallbackTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CallbackTests.swift; sourceTree = "<group>"; };
		22C0AD50278ECA9E00A96469 /* SharedStructAttributeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedStructAttributeTests.swift; sourceTree = "<group>"; };
		22D092A227B7E865009A4C2B /* AsyncTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncTests.swift; sourceTree = "<group>"; };
		22EE4E0828B5388000FEC83C /* SwiftFnUsesOpaqueSwiftType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftFnUsesOpaqueSwiftType.swift; sourceTree = "<group>"; };
		22EE4E0A28B538A700FEC83C /* SwiftFnUsesOpaqueSwiftTypeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftFnUsesOpaqueSwiftTypeTests.swift; sourceTree = "<group>"; };
		22F7CF292A42EA7800517966 /* Vec.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Vec.swift; sourceTree = "<group>"; };
		22FD1C532753CB2A00F64281 /* SwiftFnUsesOpaqueRustType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftFnUsesOpaqueRustType.swift; sourceTree = "<group>"; };
		22FD1C552753CB3F00F64281 /* SwiftFnUsesOpaqueRustTypeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftFnUsesOpaqueRustTypeTests.swift; sourceTree = "<group>"; };
		C926E4DD294F07AA0027E7E2 /* FunctionAttributes.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FunctionAttributes.swift; sourceTree = "<group>"; };
		C926E4DF294F18C50027E7E2 /* FunctionAttributeTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FunctionAttributeTests.swift; sourceTree = "<group>"; };
		DE9444DB2D5241A8007A83A4 /* SendableAttributeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SendableAttributeTests.swift; sourceTree = "<group>"; };
		DE9444DD2D527C31007A83A4 /* SendableAttribute.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SendableAttribute.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		228FE5CE2740DB6A00805D9E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				228FE60E2740F93D00805D9E /* libswift_integration_tests.a in Frameworks */,
				228FE6062740DCA100805D9E /* libresolv.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		228FE5DF2740DB6D00805D9E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		228FE5C82740DB6A00805D9E = {
			isa = PBXGroup;
			children = (
				228FE5FF2740DB8200805D9E /* Headers */,
				228FE6072740E26100805D9E /* Generated */,
				228FE5D32740DB6A00805D9E /* SwiftRustIntegrationTestRunner */,
				228FE5E52740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests */,
				228FE5D22740DB6A00805D9E /* Products */,
				228FE6022740DC7900805D9E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		228FE5D22740DB6A00805D9E /* Products */ = {
			isa = PBXGroup;
			children = (
				228FE5D12740DB6A00805D9E /* SwiftRustIntegrationTestRunner.app */,
				228FE5E22740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		228FE5D32740DB6A00805D9E /* SwiftRustIntegrationTestRunner */ = {
			isa = PBXGroup;
			children = (
				228FE5D82740DB6D00805D9E /* Assets.xcassets */,
				228FE60B2740F42000805D9E /* ASwiftStack.swift */,
				228FE5D62740DB6A00805D9E /* ContentView.swift */,
				22043296274B0AB000BAE645 /* Option.swift */,
				220432A8274D31DC00BAE645 /* Pointer.swift */,
				222A81E828EB5BB100D4A412 /* Primitive.swift */,
				228FE5DA2740DB6D00805D9E /* Preview Content */,
				220432E92753092C00BAE645 /* RustFnUsesOpaqueSwiftType.swift */,
				22BC10F52799283100A0D046 /* SharedStruct.swift */,
				22BC10F72799A3A000A0D046 /* SharedStructAttributes.swift */,
				226F944A27BF79B400243D86 /* String.swift */,
				22FD1C532753CB2A00F64281 /* SwiftFnUsesOpaqueRustType.swift */,
				228FE5DD2740DB6D00805D9E /* SwiftRustIntegrationTestRunner.entitlements */,
				228FE5D42740DB6A00805D9E /* SwiftRustIntegrationTestRunnerApp.swift */,
				22EE4E0828B5388000FEC83C /* SwiftFnUsesOpaqueSwiftType.swift */,
				22C0625228CE699D007A6F67 /* Callbacks.swift */,
				225908FD28DA0F9F0080C737 /* Result.swift */,
				DE9444DD2D527C31007A83A4 /* SendableAttribute.swift */,
				22BC4BBB294BA0EC0032B8A8 /* SharedEnumAttributes.swift */,
				C926E4DD294F07AA0027E7E2 /* FunctionAttributes.swift */,
				1784BE2729CE86D600AE5A4A /* Tuple.swift */,
				22F7CF292A42EA7800517966 /* Vec.swift */,
			);
			path = SwiftRustIntegrationTestRunner;
			sourceTree = "<group>";
		};
		228FE5DA2740DB6D00805D9E /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				228FE5DB2740DB6D00805D9E /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		228FE5E52740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests */ = {
			isa = PBXGroup;
			children = (
				221E16B52786F9FF00F94AC0 /* AlreadyDeclaredAttributeTests.swift */,
				178F1CD2298E97FB00335AA0 /* ArgumentAttributesTest.swift */,
				22D092A227B7E865009A4C2B /* AsyncTests.swift */,
				22C0625428CE6C9A007A6F67 /* CallbackTests.swift */,
				221E16B32786233600F94AC0 /* ConditionalCompilationTests.swift */,
				C926E4DF294F18C50027E7E2 /* FunctionAttributeTests.swift */,
				22046382282B4E3F00A09119 /* FunctionAttributeGetTests.swift */,
				22BCAAB827A2607700686A21 /* FunctionAttributeIdentifiableTests.swift */,
				22553323281DB5FC008A3121 /* GenericTests.rs.swift */,
				228FE60F27416C0300805D9E /* OpaqueRustStructTests.swift */,
				228FE61127428A8D00805D9E /* OpaqueSwiftStructTests.swift */,
				220432A6274C953E00BAE645 /* PointerTests.swift */,
				22043294274ADA7A00BAE645 /* OptionTests.swift */,
				225908FB28DA0E320080C737 /* ResultTests.swift */,
				222A81EA28EB5DF800D4A412 /* PrimitiveTests.swift */,
				220432EB27530AFC00BAE645 /* RustFnUsesOpaqueSwiftTypeTests.swift */,
				DE9444DB2D5241A8007A83A4 /* SendableAttributeTests.swift */,
				2202BC0727B2DD1700D43CC4 /* SharedEnumTests.swift */,
				22BC4BB9294B8CCD0032B8A8 /* SharedEnumAttributeTests.swift */,
				22C0AD50278ECA9E00A96469 /* SharedStructAttributeTests.swift */,
				220432AE274E7BF800BAE645 /* SharedStructTests.swift */,
				2289E82B29A879A7009D89D7 /* SingleRepresentationTypeElisionTests.swift */,
				228FE5E62740DB6D00805D9E /* StringTests.swift */,
				22FD1C552753CB3F00F64281 /* SwiftFnUsesOpaqueRustTypeTests.swift */,
				22EE4E0A28B538A700FEC83C /* SwiftFnUsesOpaqueSwiftTypeTests.swift */,
				1745111429BE189B00B96A1A /* TupleTests.swift */,
				22043292274A8FDF00BAE645 /* VecTests.swift */,
			);
			path = SwiftRustIntegrationTestRunnerTests;
			sourceTree = "<group>";
		};
		228FE5FF2740DB8200805D9E /* Headers */ = {
			isa = PBXGroup;
			children = (
				228FE6012740DC2000805D9E /* BridgingHeader.h */,
			);
			path = Headers;
			sourceTree = "<group>";
		};
		228FE6022740DC7900805D9E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				228FE6052740DC9A00805D9E /* libresolv.tbd */,
				228FE6032740DC7900805D9E /* libswift_integration_tests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		228FE6072740E26100805D9E /* Generated */ = {
			isa = PBXGroup;
			children = (
				228FE649274919C600805D9E /* swift-integration-tests.h */,
				228FE648274919C500805D9E /* swift-integration-tests.swift */,
				228FE6472749127400805D9E /* SwiftBridgeCore.h */,
				228FE64427480E1C00805D9E /* SwiftBridgeCore.swift */,
			);
			path = Generated;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		228FE5D02740DB6A00805D9E /* SwiftRustIntegrationTestRunner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 228FE5F62740DB6D00805D9E /* Build configuration list for PBXNativeTarget "SwiftRustIntegrationTestRunner" */;
			buildPhases = (
				228FE60D2740F66400805D9E /* Run Script */,
				228FE5CD2740DB6A00805D9E /* Sources */,
				228FE5CE2740DB6A00805D9E /* Frameworks */,
				228FE5CF2740DB6A00805D9E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftRustIntegrationTestRunner;
			packageProductDependencies = (
			);
			productName = SwiftRustIntegrationTestRunner;
			productReference = 228FE5D12740DB6A00805D9E /* SwiftRustIntegrationTestRunner.app */;
			productType = "com.apple.product-type.application";
		};
		228FE5E12740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 228FE5F92740DB6D00805D9E /* Build configuration list for PBXNativeTarget "SwiftRustIntegrationTestRunnerTests" */;
			buildPhases = (
				228FE5DE2740DB6D00805D9E /* Sources */,
				228FE5DF2740DB6D00805D9E /* Frameworks */,
				228FE5E02740DB6D00805D9E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				228FE5E42740DB6D00805D9E /* PBXTargetDependency */,
			);
			name = SwiftRustIntegrationTestRunnerTests;
			productName = SwiftRustIntegrationTestRunnerTests;
			productReference = 228FE5E22740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		228FE5C92740DB6A00805D9E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1300;
				LastUpgradeCheck = 1300;
				TargetAttributes = {
					228FE5D02740DB6A00805D9E = {
						CreatedOnToolsVersion = 13.0;
					};
					228FE5E12740DB6D00805D9E = {
						CreatedOnToolsVersion = 13.0;
						TestTargetID = 228FE5D02740DB6A00805D9E;
					};
				};
			};
			buildConfigurationList = 228FE5CC2740DB6A00805D9E /* Build configuration list for PBXProject "SwiftRustIntegrationTestRunner" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 228FE5C82740DB6A00805D9E;
			productRefGroup = 228FE5D22740DB6A00805D9E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				228FE5D02740DB6A00805D9E /* SwiftRustIntegrationTestRunner */,
				228FE5E12740DB6D00805D9E /* SwiftRustIntegrationTestRunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		228FE5CF2740DB6A00805D9E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				228FE5DC2740DB6D00805D9E /* Preview Assets.xcassets in Resources */,
				228FE5D92740DB6D00805D9E /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		228FE5E02740DB6D00805D9E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		228FE60D2740F66400805D9E /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "./build-rust.sh\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		228FE5CD2740DB6A00805D9E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22BC10F62799283100A0D046 /* SharedStruct.swift in Sources */,
				1784BE2829CE86D600AE5A4A /* Tuple.swift in Sources */,
				226F944B27BF79B400243D86 /* String.swift in Sources */,
				22043297274B0AB000BAE645 /* Option.swift in Sources */,
				220432EA2753092C00BAE645 /* RustFnUsesOpaqueSwiftType.swift in Sources */,
				22BC4BBC294BA0EC0032B8A8 /* SharedEnumAttributes.swift in Sources */,
				22F7CF2A2A42EA7800517966 /* Vec.swift in Sources */,
				22FD1C542753CB2A00F64281 /* SwiftFnUsesOpaqueRustType.swift in Sources */,
				220432A9274D31DC00BAE645 /* Pointer.swift in Sources */,
				225908FE28DA0F9F0080C737 /* Result.swift in Sources */,
				228FE5D72740DB6A00805D9E /* ContentView.swift in Sources */,
				222A81E928EB5BB100D4A412 /* Primitive.swift in Sources */,
				228FE64627480E1D00805D9E /* SwiftBridgeCore.swift in Sources */,
				228FE5D52740DB6A00805D9E /* SwiftRustIntegrationTestRunnerApp.swift in Sources */,
				C926E4DE294F07AA0027E7E2 /* FunctionAttributes.swift in Sources */,
				228FE64A274919C600805D9E /* swift-integration-tests.swift in Sources */,
				22C0625328CE699D007A6F67 /* Callbacks.swift in Sources */,
				DE9444DE2D527C3B007A83A4 /* SendableAttribute.swift in Sources */,
				22BC10F82799A3A000A0D046 /* SharedStructAttributes.swift in Sources */,
				22EE4E0928B5388000FEC83C /* SwiftFnUsesOpaqueSwiftType.swift in Sources */,
				228FE60C2740F42000805D9E /* ASwiftStack.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		228FE5DE2740DB6D00805D9E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22043293274A8FDF00BAE645 /* VecTests.swift in Sources */,
				221E16B62786F9FF00F94AC0 /* AlreadyDeclaredAttributeTests.swift in Sources */,
				220432A7274C953E00BAE645 /* PointerTests.swift in Sources */,
				C926E4E0294F18C50027E7E2 /* FunctionAttributeTests.swift in Sources */,
				220432AF274E7BF800BAE645 /* SharedStructTests.swift in Sources */,
				178F1CD3298E97FB00335AA0 /* ArgumentAttributesTest.swift in Sources */,
				2289E82C29A879A7009D89D7 /* SingleRepresentationTypeElisionTests.swift in Sources */,
				DE9444DC2D5241AD007A83A4 /* SendableAttributeTests.swift in Sources */,
				220432EC27530AFC00BAE645 /* RustFnUsesOpaqueSwiftTypeTests.swift in Sources */,
				222A81EB28EB5DF800D4A412 /* PrimitiveTests.swift in Sources */,
				22553324281DB5FC008A3121 /* GenericTests.rs.swift in Sources */,
				225908FC28DA0E320080C737 /* ResultTests.swift in Sources */,
				2202BC0827B2DD1700D43CC4 /* SharedEnumTests.swift in Sources */,
				22BCAAB927A2607700686A21 /* FunctionAttributeIdentifiableTests.swift in Sources */,
				22EE4E0B28B538A700FEC83C /* SwiftFnUsesOpaqueSwiftTypeTests.swift in Sources */,
				221E16B42786233600F94AC0 /* ConditionalCompilationTests.swift in Sources */,
				22043295274ADA7A00BAE645 /* OptionTests.swift in Sources */,
				22C0AD51278ECA9E00A96469 /* SharedStructAttributeTests.swift in Sources */,
				228FE5E72740DB6D00805D9E /* StringTests.swift in Sources */,
				22C0625528CE6C9A007A6F67 /* CallbackTests.swift in Sources */,
				22BC4BBA294B8CCD0032B8A8 /* SharedEnumAttributeTests.swift in Sources */,
				228FE61227428A8D00805D9E /* OpaqueSwiftStructTests.swift in Sources */,
				22FD1C562753CB3F00F64281 /* SwiftFnUsesOpaqueRustTypeTests.swift in Sources */,
				228FE61027416C0300805D9E /* OpaqueRustStructTests.swift in Sources */,
				22D092A327B7E865009A4C2B /* AsyncTests.swift in Sources */,
				1745111529BE189B00B96A1A /* TupleTests.swift in Sources */,
				22046383282B4E3F00A09119 /* FunctionAttributeGetTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		228FE5E42740DB6D00805D9E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 228FE5D02740DB6A00805D9E /* SwiftRustIntegrationTestRunner */;
			targetProxy = 228FE5E32740DB6D00805D9E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		228FE5F42740DB6D00805D9E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		228FE5F52740DB6D00805D9E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		228FE5F72740DB6D00805D9E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SwiftRustIntegrationTestRunner/SwiftRustIntegrationTestRunner.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SwiftRustIntegrationTestRunner/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(PROJECT_DIR)/../target/debug";
				MACOSX_DEPLOYMENT_TARGET = 11.5;
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.xcode.SwiftRustIntegrationTestRunner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG FOO";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Headers/BridgingHeader.h";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		228FE5F82740DB6D00805D9E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SwiftRustIntegrationTestRunner/SwiftRustIntegrationTestRunner.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SwiftRustIntegrationTestRunner/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(PROJECT_DIR)/../target/release";
				MACOSX_DEPLOYMENT_TARGET = 11.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.xcode.SwiftRustIntegrationTestRunner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Headers/BridgingHeader.h";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		228FE5FA2740DB6D00805D9E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MACOSX_DEPLOYMENT_TARGET = 11.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.xcode.SwiftRustIntegrationTestRunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SwiftRustIntegrationTestRunner.app/Contents/MacOS/SwiftRustIntegrationTestRunner";
			};
			name = Debug;
		};
		228FE5FB2740DB6D00805D9E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MACOSX_DEPLOYMENT_TARGET = 11.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.xcode.SwiftRustIntegrationTestRunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SwiftRustIntegrationTestRunner.app/Contents/MacOS/SwiftRustIntegrationTestRunner";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		228FE5CC2740DB6A00805D9E /* Build configuration list for PBXProject "SwiftRustIntegrationTestRunner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				228FE5F42740DB6D00805D9E /* Debug */,
				228FE5F52740DB6D00805D9E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		228FE5F62740DB6D00805D9E /* Build configuration list for PBXNativeTarget "SwiftRustIntegrationTestRunner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				228FE5F72740DB6D00805D9E /* Debug */,
				228FE5F82740DB6D00805D9E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		228FE5F92740DB6D00805D9E /* Build configuration list for PBXNativeTarget "SwiftRustIntegrationTestRunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				228FE5FA2740DB6D00805D9E /* Debug */,
				228FE5FB2740DB6D00805D9E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 228FE5C92740DB6A00805D9E /* Project object */;
}
