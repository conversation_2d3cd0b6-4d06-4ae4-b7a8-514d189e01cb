//
//  SharedStructAttributes.swift
//  SwiftRustIntegrationTestRunner
//
//  Created by <PERSON> on 1/20/22.
//

import Foundation

func extern_swift_struct_rename_1(arg: StructRename1) -> StructRename1 {
    arg
}

func extern_swift_struct_rename_2(arg: StructRename2) -> StructRename2 {
    arg
}

func extern_swift_struct_rename_3(arg: StructRename3) -> StructRename3 {
    arg
}

func swift_reflect_already_declared_struct(arg: AlreadyDeclaredStructTest) -> AlreadyDeclaredStructTest {
    arg
}

