//! 系统相册访问演示程序
//!
//! 这是一个真实的应用程序，使用 system-photo-access 库访问系统相册。
//! 可以通过 `cargo run` 命令运行。

use std::env;
use std::process;

// 导入库的功能
use system_photo_access::{
    check_permission_status, ensure_photo_permission, get_asset_count, get_photo_assets,
    get_thumbnail, init, open_settings, request_permission, ImageFormat, ImageSize, MediaType,
    PhotoAsset, PhotoError, PhotoPermissionStatus, PhotoQuery, SortOrder, VERSION,
};

#[tokio::main]
async fn main() {
    // 解析命令行参数
    let args: Vec<String> = env::args().collect();

    if args.len() > 1 {
        match args[1].as_str() {
            "help" | "--help" | "-h" => {
                print_help();
                return;
            }
            "version" | "--version" | "-v" => {
                print_version();
                return;
            }
            "demo" => {
                if let Err(e) = run_demo().await {
                    eprintln!("演示程序运行失败: {}", e);
                    process::exit(1);
                }
                return;
            }
            "check" => {
                if let Err(e) = check_permissions().await {
                    eprintln!("权限检查失败: {}", e);
                    process::exit(1);
                }
                return;
            }
            "count" => {
                if let Err(e) = show_asset_count().await {
                    eprintln!("获取资源数量失败: {}", e);
                    process::exit(1);
                }
                return;
            }
            "list" => {
                let limit = if args.len() > 2 {
                    args[2].parse().unwrap_or(10)
                } else {
                    10
                };
                if let Err(e) = list_assets(limit).await {
                    eprintln!("列出资源失败: {}", e);
                    process::exit(1);
                }
                return;
            }
            "thumbnail" => {
                if args.len() < 3 {
                    eprintln!("用法: cargo run thumbnail <asset_id>");
                    process::exit(1);
                }
                let asset_id = &args[2];
                if let Err(e) = show_thumbnail(asset_id).await {
                    eprintln!("获取缩略图失败: {}", e);
                    process::exit(1);
                }
                return;
            }
            _ => {
                eprintln!("未知命令: {}", args[1]);
                print_help();
                process::exit(1);
            }
        }
    }

    // 默认运行演示
    if let Err(e) = run_demo().await {
        eprintln!("演示程序运行失败: {}", e);
        process::exit(1);
    }
}

/// 打印帮助信息
fn print_help() {
    println!("系统相册访问演示程序");
    println!();
    println!("这是一个真实的应用程序，使用 swift-bridge 与 iOS/macOS 系统相册集成。");
    println!();
    println!("用法:");
    println!("  cargo run                         运行完整演示");
    println!("  cargo run demo                    运行完整演示");
    println!("  cargo run check                   检查权限状态");
    println!("  cargo run count                   显示资源数量统计");
    println!("  cargo run list [数量]             列出资源 (默认10个)");
    println!("  cargo run thumbnail <asset_id>    获取指定资源的缩略图");
    println!("  cargo run help                    显示此帮助信息");
    println!("  cargo run version                 显示版本信息");
    println!();
    println!("示例:");
    println!("  cargo run demo                    # 运行完整功能演示");
    println!("  cargo run check                   # 仅检查权限状态");
    println!("  cargo run count                   # 显示各类型资源数量");
    println!("  cargo run list 20                 # 列出前20个资源");
    println!("  cargo run thumbnail asset-123     # 获取指定资源的缩略图");
    println!();
    println!("注意:");
    println!("  此项目需要在 macOS 或 iOS 环境中运行才能访问系统相册。");
    println!("  首次运行时需要授予相册访问权限。");
}

/// 打印版本信息
fn print_version() {
    println!("系统相册访问库 v{}", VERSION);
    println!("基于 swift-bridge 构建");
}

/// 运行完整演示 - 使用真实的库函数
async fn run_demo() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    init();

    println!("🎯 系统相册访问库演示");
    println!("📱 版本: {}", VERSION);
    println!("{}", "=".repeat(50));

    println!("\n📋 1. 检查权限状态");

    // 检查权限状态
    let status = check_permission_status()?;
    println!("当前权限状态: {:?}", status);
    println!("状态描述: {}", status.description());
    println!(
        "是否有访问权限: {}",
        if status.has_access() {
            "✅ 是"
        } else {
            "❌ 否"
        }
    );

    // 使用 ensure_photo_permission 来确保权限
    println!("\n📋 2. 确保拥有权限");
    match ensure_photo_permission().await {
        Ok(final_status) => {
            println!("权限确认结果: {:?}", final_status);

            if !final_status.has_access() {
                println!("❌ 无法获得相册访问权限");
                println!("💡 请在系统设置中授予相册访问权限");

                // 尝试打开设置
                if let Err(e) = open_settings() {
                    println!("❌ 无法打开设置页面: {}", e);
                }

                return Err("权限不足".into());
            }
        }
        Err(PhotoError::PermissionDenied) => {
            println!("❌ 权限被拒绝");
            println!("💡 尝试打开设置页面...");
            if let Err(e) = open_settings() {
                println!("❌ 无法打开设置页面: {}", e);
            }
            return Err("权限被拒绝".into());
        }
        Err(PhotoError::PermissionRestricted) => {
            println!("❌ 权限受限 (可能由于家长控制)");
            return Err("权限受限".into());
        }
        Err(error) => {
            println!("❌ 权限检查失败: {}", error);
            return Err(error.into());
        }
    }

    println!("\n📊 3. 获取资源统计");

    // 获取总资源数量
    match get_asset_count(None).await {
        Ok(total) => println!("📊 总资源数量: {}", total),
        Err(e) => println!("❌ 获取总数失败: {}", e),
    }

    // 获取图片数量
    match get_asset_count(Some(MediaType::Image)).await {
        Ok(images) => println!("🖼️  图片数量: {}", images),
        Err(e) => println!("❌ 获取图片数失败: {}", e),
    }

    // 获取视频数量
    match get_asset_count(Some(MediaType::Video)).await {
        Ok(videos) => println!("🎥 视频数量: {}", videos),
        Err(e) => println!("❌ 获取视频数失败: {}", e),
    }

    println!("\n📷 4. 获取相册资源");

    // 获取前10个图片资源
    let query = PhotoQuery::images_only()
        .with_pagination(0, 10)
        .with_sort_order(SortOrder::CreationDateDescending);

    match get_photo_assets(query).await {
        Ok(assets) => {
            println!("找到 {} 个图片资源", assets.len());

            // 显示前几个资源的详细信息
            for (index, asset) in assets.iter().take(5).enumerate() {
                println!("  {}. ID: {}", index + 1, asset.id);
                println!("     类型: {:?}", asset.media_type);
                println!("     尺寸: {}x{}", asset.pixel_width, asset.pixel_height);

                if let Some(creation_date) = &asset.creation_date {
                    println!(
                        "     创建时间: {}",
                        creation_date.format("%Y-%m-%d %H:%M:%S")
                    );
                }

                if let Some(file_size) = asset.file_size {
                    println!("     文件大小: {} KB", file_size / 1024);
                }

                println!(
                    "     是否收藏: {}",
                    if asset.is_favorite { "是" } else { "否" }
                );
                println!(
                    "     是否在云端: {}",
                    if asset.is_in_cloud { "是" } else { "否" }
                );
                println!();
            }

            if assets.len() > 5 {
                println!("... 还有 {} 个资源", assets.len() - 5);
            }

            // 如果有资源，尝试获取第一个的缩略图
            if let Some(first_asset) = assets.first() {
                println!("\n🖼️  5. 获取缩略图演示");
                println!("🔍 测试资源: {}", first_asset.id);

                let thumbnail_size = ImageSize::new(200, 200);
                match get_thumbnail(&first_asset.id, thumbnail_size).await {
                    Ok(image_data) => {
                        println!("✅ 缩略图获取成功:");
                        println!(
                            "   📐 尺寸: {}x{}",
                            image_data.size.width, image_data.size.height
                        );
                        println!("   📄 格式: {:?}", image_data.format);
                        println!("   💾 大小: {} 字节", image_data.byte_size);
                        println!("   🏷️  MIME: {}", image_data.mime_type());
                    }
                    Err(error) => {
                        println!("❌ 缩略图获取失败: {}", error);
                        if let Some(action) = error.suggested_action() {
                            println!("💡 建议: {}", action);
                        }
                    }
                }
            }
        }
        Err(error) => {
            println!("❌ 获取相册列表失败: {}", error);
            return Err(error.into());
        }
    }

    println!("\n✅ 演示完成!");
    Ok(())
}

/// 检查权限状态
async fn check_permissions() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    init();

    println!("🔐 权限状态检查");
    println!("{}", "=".repeat(20));

    match check_permission_status() {
        Ok(status) => {
            println!("当前状态: {:?}", status);
            println!("状态描述: {}", status.description());
            println!(
                "有访问权限: {}",
                if status.has_access() {
                    "✅ 是"
                } else {
                    "❌ 否"
                }
            );

            if !status.has_access() {
                println!("\n💡 提示:");
                println!("  - 运行 'cargo run demo' 来请求权限");
                println!("  - 或在系统设置中手动授予权限");

                // 提供打开设置的选项
                println!("\n🔧 是否要打开系统设置? (y/N)");
                let mut input = String::new();
                if std::io::stdin().read_line(&mut input).is_ok() {
                    if input.trim().to_lowercase() == "y" {
                        if let Err(e) = open_settings() {
                            println!("❌ 无法打开设置页面: {}", e);
                        }
                    }
                }
            } else {
                println!("\n✅ 权限状态良好，可以访问相册");

                // 如果有权限，显示一些基本统计
                println!("\n📊 快速统计:");
                match get_asset_count(None).await {
                    Ok(total) => println!("  总资源数量: {}", total),
                    Err(e) => println!("  获取总数失败: {}", e),
                }

                match get_asset_count(Some(MediaType::Image)).await {
                    Ok(images) => println!("  图片数量: {}", images),
                    Err(e) => println!("  获取图片数失败: {}", e),
                }

                match get_asset_count(Some(MediaType::Video)).await {
                    Ok(videos) => println!("  视频数量: {}", videos),
                    Err(e) => println!("  获取视频数失败: {}", e),
                }
            }
        }
        Err(error) => {
            println!("❌ 权限检查失败: {}", error);
            return Err(error.into());
        }
    }

    Ok(())
}

/// 显示资源数量统计
async fn show_asset_count() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    init();

    println!("📊 相册资源统计");
    println!("{}", "=".repeat(20));

    // 获取总资源数量
    match get_asset_count(None).await {
        Ok(total) => println!("📊 总资源数量: {}", total),
        Err(e) => println!("❌ 获取总数失败: {}", e),
    }

    // 获取图片数量
    match get_asset_count(Some(MediaType::Image)).await {
        Ok(images) => println!("🖼️  图片数量: {}", images),
        Err(e) => println!("❌ 获取图片数失败: {}", e),
    }

    // 获取视频数量
    match get_asset_count(Some(MediaType::Video)).await {
        Ok(videos) => println!("🎥 视频数量: {}", videos),
        Err(e) => println!("❌ 获取视频数失败: {}", e),
    }

    // 获取音频数量
    match get_asset_count(Some(MediaType::Audio)).await {
        Ok(audios) => println!("🎵 音频数量: {}", audios),
        Err(e) => println!("❌ 获取音频数失败: {}", e),
    }

    Ok(())
}

/// 列出相册资源
async fn list_assets(limit: usize) -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    init();

    println!("📋 相册资源列表 (前 {} 个)", limit);
    println!("{}", "=".repeat(30));

    // 检查权限
    let status = check_permission_status()?;
    if !status.has_access() {
        println!("❌ 没有相册访问权限");
        println!("💡 请先运行 'cargo run demo' 来获取权限");
        return Ok(());
    }

    // 获取资源列表
    let query = PhotoQuery::default()
        .with_pagination(0, limit)
        .with_sort_order(SortOrder::CreationDateDescending);

    match get_photo_assets(query).await {
        Ok(assets) => {
            if assets.is_empty() {
                println!("📭 没有找到资源");
                return Ok(());
            }

            println!("找到 {} 个资源:", assets.len());
            println!();

            for (index, asset) in assets.iter().enumerate() {
                let type_icon = match asset.media_type {
                    MediaType::Image => "🖼️",
                    MediaType::Video => "🎥",
                    MediaType::Audio => "🎵",
                    MediaType::Unknown => "❓",
                };

                println!("{}. {} ID: {}", index + 1, type_icon, asset.id);
                println!("   📐 尺寸: {}x{}", asset.pixel_width, asset.pixel_height);

                if let Some(creation_date) = &asset.creation_date {
                    println!("   📅 创建: {}", creation_date.format("%Y-%m-%d %H:%M:%S"));
                }

                if let Some(file_size) = asset.file_size {
                    println!("   💾 大小: {} KB", file_size / 1024);
                }

                println!(
                    "   ⭐ 收藏: {} | ☁️ 云端: {}",
                    if asset.is_favorite { "是" } else { "否" },
                    if asset.is_in_cloud { "是" } else { "否" }
                );
                println!();
            }
        }
        Err(error) => {
            println!("❌ 获取资源列表失败: {}", error);
            return Err(error.into());
        }
    }

    Ok(())
}

/// 显示指定资源的缩略图信息
async fn show_thumbnail(asset_id: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    init();

    println!("🖼️  缩略图信息");
    println!("{}", "=".repeat(15));
    println!("🔍 资源ID: {}", asset_id);

    // 检查权限
    let status = check_permission_status()?;
    if !status.has_access() {
        println!("❌ 没有相册访问权限");
        println!("💡 请先运行 'cargo run demo' 来获取权限");
        return Ok(());
    }

    // 获取缩略图
    let thumbnail_size = ImageSize::new(200, 200);
    match get_thumbnail(asset_id, thumbnail_size).await {
        Ok(image_data) => {
            println!("✅ 缩略图获取成功:");
            println!("   📐 请求尺寸: 200x200");
            println!(
                "   📐 实际尺寸: {}x{}",
                image_data.size.width, image_data.size.height
            );
            println!("   📄 格式: {:?}", image_data.format);
            println!("   💾 大小: {} 字节", image_data.byte_size);
            println!("   🏷️  MIME: {}", image_data.mime_type());

            // 可以选择保存到文件
            println!("\n💾 是否保存到文件? (y/N)");
            let mut input = String::new();
            if std::io::stdin().read_line(&mut input).is_ok() {
                if input.trim().to_lowercase() == "y" {
                    let extension = match image_data.format {
                        ImageFormat::JPEG => "jpg",
                        ImageFormat::PNG => "png",
                        ImageFormat::HEIC => "heic",
                        ImageFormat::GIF => "gif",
                        ImageFormat::TIFF => "tiff",
                        ImageFormat::Unknown(_) => "bin",
                    };

                    let filename =
                        format!("thumbnail_{}.{}", asset_id.replace("/", "_"), extension);

                    match std::fs::write(&filename, &image_data.data) {
                        Ok(_) => println!("✅ 已保存到: {}", filename),
                        Err(e) => println!("❌ 保存失败: {}", e),
                    }
                }
            }
        }
        Err(error) => {
            println!("❌ 缩略图获取失败: {}", error);
            if let Some(action) = error.suggested_action() {
                println!("💡 建议: {}", action);
            }
            return Err(error.into());
        }
    }

    Ok(())
}
