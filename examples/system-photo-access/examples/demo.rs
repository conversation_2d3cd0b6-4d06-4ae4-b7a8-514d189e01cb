//! 系统相册访问演示程序
//!
//! 这是一个简单的演示程序，展示 swift-bridge 的基本功能。

use system_photo_access::{
    call_swift_hello, check_permission_status_simple, get_asset_count_simple,
};

fn main() {
    println!("🎯 Swift-Bridge 演示程序");
    println!("========================");

    // 1. 调用 Swift 函数
    println!("\n1. 调用 Swift 函数:");
    call_swift_hello();

    // 2. 检查权限状态
    println!("\n2. 检查权限状态:");
    let status = check_permission_status_simple();
    println!("权限状态代码: {}", status);
    match status {
        0 => println!("状态: 未确定"),
        1 => println!("状态: 受限"),
        2 => println!("状态: 拒绝"),
        3 => println!("状态: 已授权"),
        4 => println!("状态: 有限访问"),
        _ => println!("状态: 未知"),
    }

    // 3. 获取资源数量
    println!("\n3. 获取资源数量:");
    let count = get_asset_count_simple(0); // 0 表示所有类型
    println!("总资源数量: {}", count);

    let image_count = get_asset_count_simple(1); // 1 表示图片
    println!("图片数量: {}", image_count);

    let video_count = get_asset_count_simple(2); // 2 表示视频
    println!("视频数量: {}", video_count);

    println!("\n✅ 演示完成!");
}
