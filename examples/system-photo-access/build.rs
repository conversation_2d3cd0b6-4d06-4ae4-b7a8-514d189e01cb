use std::path::PathBuf;
use std::process::Command;

fn main() {
    let out_dir = PathBuf::from("./generated");

    let bridges = vec!["src/lib.rs"];
    for bridge in &bridges {
        println!("cargo:rerun-if-changed={}", bridge);
    }

    // 监听 Swift 源文件变化
    println!("cargo:rerun-if-changed=swift/Sources/SystemPhotoAccess/PhotoBridge.swift");

    swift_bridge_build::parse_bridges(bridges)
        .write_all_concatenated(out_dir, env!("CARGO_PKG_NAME"));

    // 编译 Swift 代码
    build_swift_library();
}

fn build_swift_library() {
    // 检查是否在 macOS 上
    if !cfg!(target_os = "macos") {
        println!("cargo:warning=Swift compilation is only supported on macOS");
        return;
    }

    // 构建 Swift 库
    let output = Command::new("swift")
        .args(&[
            "build",
            "-c",
            "release",
            "--package-path",
            "swift",
            "--build-path",
            ".build",
        ])
        .output()
        .expect("Failed to execute swift build command");

    if !output.status.success() {
        panic!(
            "Swift build failed:\nstdout: {}\nstderr: {}",
            String::from_utf8_lossy(&output.stdout),
            String::from_utf8_lossy(&output.stderr)
        );
    }

    // 链接 Swift 库
    println!("cargo:rustc-link-search=native=.build/release");
    println!("cargo:rustc-link-lib=static=SystemPhotoAccess");

    // 链接系统框架
    println!("cargo:rustc-link-lib=framework=Photos");
    println!("cargo:rustc-link-lib=framework=PhotosUI");
    println!("cargo:rustc-link-lib=framework=Foundation");
    println!("cargo:rustc-link-lib=framework=CoreFoundation");

    #[cfg(target_os = "ios")]
    {
        println!("cargo:rustc-link-lib=framework=UIKit");
    }

    #[cfg(target_os = "macos")]
    {
        println!("cargo:rustc-link-lib=framework=AppKit");
    }
}
