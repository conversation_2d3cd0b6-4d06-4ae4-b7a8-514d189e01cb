//
//  PhotoBridge.swift
//  SystemPhotoAccess
//
//  Created by swift-bridge on 2025-01-08.
//  Copyright © 2025 swift-bridge contributors. All rights reserved.
//

import Foundation
import Photos
import PhotosUI

#if os(iOS)
import UIKit
#elseif os(macOS)
import AppKit
#endif

/// 错误域常量
public let PhotoLibraryErrorDomain = "PhotoLibraryErrorDomain"

/// 错误代码枚举
public enum PhotoErrorCode: Int32 {
    case permissionDenied = 1001
    case permissionRestricted = 1002
    case assetNotFound = 2001
    case networkError = 3001
    case systemError = 4001
    case unsupportedFormat = 5001
    case outOfMemory = 6001
    case cancelled = 7001
    case timeout = 8001
    case swiftBridgeError = 9001
}

/// 权限引导信息结构
public struct PermissionGuidance {
    public let title: String
    public let message: String
    public let actionTitle: String
    public let steps: [String]
    public let canOpenSettings: Bool
}

/// 权限状态监听回调类型
public typealias PermissionStatusChangeCallback = @convention(c) (Int32) -> Void

/// 系统相册访问桥接模块
///
/// 这个模块提供了 Rust 和 Swift 之间的桥接功能，
/// 用于访问 iOS/macOS 系统相册。
public class PhotoBridge {

    // MARK: - 单例模式

    public static let shared = PhotoBridge()

    // MARK: - 权限监听相关属性

    /// 权限状态变化回调
    private var permissionStatusCallback: PermissionStatusChangeCallback?

    /// 应用状态监听器
    private var appStateObserver: NSObjectProtocol?

    /// 当前监听的权限状态
    private var lastKnownPermissionStatus: PHAuthorizationStatus?

    private init() {
        // 私有初始化，确保单例模式
        setupAppStateMonitoring()
    }

    deinit {
        stopPermissionStatusMonitoring()
    }

    // MARK: - 应用状态监听设置

    /// 设置应用状态监听
    private func setupAppStateMonitoring() {
        #if os(iOS)
        appStateObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.checkPermissionStatusChange()
        }
        #elseif os(macOS)
        appStateObserver = NotificationCenter.default.addObserver(
            forName: NSApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.checkPermissionStatusChange()
        }
        #endif
    }

    /// 检查权限状态变化
    private func checkPermissionStatusChange() {
        let currentStatus = PHPhotoLibrary.authorizationStatus()

        if let lastStatus = lastKnownPermissionStatus, lastStatus != currentStatus {
            // 权限状态发生变化，通知回调
            let statusCode = mapAuthorizationStatusToInt(currentStatus)
            permissionStatusCallback?(statusCode)
        }

        lastKnownPermissionStatus = currentStatus
    }

    // MARK: - 错误处理

    /// 创建 NSError 对象
    ///
    /// - Parameters:
    ///   - code: 错误代码
    ///   - message: 错误消息
    ///   - userInfo: 额外的用户信息
    /// - Returns: NSError 对象
    private func createError(code: PhotoErrorCode, message: String, userInfo: [String: Any]? = nil) -> NSError {
        var info: [String: Any] = [
            NSLocalizedDescriptionKey: message,
            "PhotoErrorCode": code.rawValue
        ]

        if let additionalInfo = userInfo {
            info.merge(additionalInfo) { (_, new) in new }
        }

        return NSError(domain: PhotoLibraryErrorDomain, code: Int(code.rawValue), userInfo: info)
    }

    /// 将 NSError 转换为错误信息结构
    ///
    /// - Parameter error: NSError 对象
    /// - Returns: 错误信息字典
    private func errorToInfo(_ error: NSError) -> [String: Any] {
        return [
            "code": error.code,
            "domain": error.domain,
            "message": error.localizedDescription,
            "userInfo": error.userInfo
        ]
    }

    /// 记录错误日志
    ///
    /// - Parameters:
    ///   - error: 错误对象
    ///   - operation: 操作名称
    private func logError(_ error: NSError, operation: String) {
        print("PhotoBridge Error - Operation: \(operation)")
        print("  Code: \(error.code)")
        print("  Domain: \(error.domain)")
        print("  Message: \(error.localizedDescription)")
        if !error.userInfo.isEmpty {
            print("  UserInfo: \(error.userInfo)")
        }
    }

    // MARK: - 权限引导功能

    /// 获取权限引导信息
    ///
    /// - Parameter status: 权限状态
    /// - Returns: 权限引导信息
    public func getPermissionGuidance(for status: PHAuthorizationStatus) -> PermissionGuidance {
        switch status {
        case .notDetermined:
            return PermissionGuidance(
                title: "需要相册访问权限",
                message: "为了让您能够选择和管理照片，我们需要访问您的相册。",
                actionTitle: "授权访问",
                steps: [
                    "点击\"授权访问\"按钮",
                    "在弹出的权限对话框中选择\"允许访问所有照片\"或\"选择照片\"",
                    "完成授权后即可开始使用相册功能"
                ],
                canOpenSettings: false
            )

        case .denied:
            var steps: [String] = ["点击\"前往设置\"按钮打开系统设置"]
            #if os(iOS)
            steps.append("找到并点击当前应用")
            steps.append("点击\"照片\"选项")
            steps.append("选择\"所有照片\"或\"选定的照片\"")
            #else
            steps.append("点击\"安全性与隐私\"")
            steps.append("选择\"隐私\"标签页")
            steps.append("在左侧列表中选择\"照片\"")
            steps.append("勾选当前应用的复选框")
            #endif

            return PermissionGuidance(
                title: "相册访问权限被拒绝",
                message: "您之前拒绝了相册访问权限。要使用相册功能，请在设置中重新开启权限。",
                actionTitle: "前往设置",
                steps: steps,
                canOpenSettings: true
            )

        case .restricted:
            return PermissionGuidance(
                title: "相册访问权限受限",
                message: "由于设备限制（如家长控制），无法访问相册。请联系设备管理员或检查设备设置。",
                actionTitle: "查看设置",
                steps: [
                    "检查设备是否启用了家长控制或企业管理",
                    "联系设备管理员获取权限",
                    "或在设备设置中调整限制选项"
                ],
                canOpenSettings: true
            )

        case .authorized:
            return PermissionGuidance(
                title: "已获得完全访问权限",
                message: "您已授权应用访问所有照片，可以正常使用相册功能。",
                actionTitle: "继续使用",
                steps: ["您可以正常使用所有相册功能"],
                canOpenSettings: false
            )

        case .limited:
            return PermissionGuidance(
                title: "已获得有限访问权限",
                message: "您已授权应用访问选定的照片。如需访问更多照片，可以在设置中调整权限。",
                actionTitle: "管理权限",
                steps: [
                    "点击\"管理权限\"可以选择更多照片",
                    "或前往设置中将权限改为\"所有照片\""
                ],
                canOpenSettings: true
            )

        @unknown default:
            return PermissionGuidance(
                title: "未知权限状态",
                message: "检测到未知的权限状态，建议重新请求权限。",
                actionTitle: "重新请求",
                steps: ["请重新启动应用并重新请求权限"],
                canOpenSettings: true
            )
        }
    }

    /// 获取当前权限状态的引导信息
    ///
    /// - Returns: 当前权限状态对应的引导信息
    public func getCurrentPermissionGuidance() -> PermissionGuidance {
        let currentStatus = PHPhotoLibrary.authorizationStatus()
        return getPermissionGuidance(for: currentStatus)
    }

    /// 开始权限状态监听
    ///
    /// - Parameter callback: 权限状态变化时的回调函数
    public func startPermissionStatusMonitoring(callback: @escaping PermissionStatusChangeCallback) {
        permissionStatusCallback = callback
        lastKnownPermissionStatus = PHPhotoLibrary.authorizationStatus()
    }

    /// 停止权限状态监听
    public func stopPermissionStatusMonitoring() {
        permissionStatusCallback = nil
        lastKnownPermissionStatus = nil

        if let observer = appStateObserver {
            NotificationCenter.default.removeObserver(observer)
            appStateObserver = nil
        }
    }

    // MARK: - 权限管理

    /// 检查当前相册访问权限状态
    ///
    /// - Returns: 权限状态的整数表示
    public func checkPermissionStatus() -> Int32 {
        let status = PHPhotoLibrary.authorizationStatus()
        return mapAuthorizationStatusToInt(status)
    }

    /// 请求相册访问权限
    ///
    /// - Parameter completion: 权限请求完成后的回调，返回权限状态或错误信息
    public func requestPermission(completion: @escaping (Int32, [String: Any]?) -> Void) {
        #if os(iOS)
        if #available(iOS 14, *) {
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                DispatchQueue.main.async {
                    let statusCode = self.mapAuthorizationStatusToInt(status)
                    var errorInfo: [String: Any]? = nil

                    // 如果权限被拒绝或受限，创建错误信息
                    if status == .denied {
                        let error = self.createError(
                            code: .permissionDenied,
                            message: "相册访问权限被拒绝",
                            userInfo: ["operation": "requestPermission", "platform": "iOS"]
                        )
                        errorInfo = self.errorToInfo(error)
                        self.logError(error, operation: "requestPermission")
                    } else if status == .restricted {
                        let error = self.createError(
                            code: .permissionRestricted,
                            message: "相册访问权限受限",
                            userInfo: ["operation": "requestPermission", "platform": "iOS"]
                        )
                        errorInfo = self.errorToInfo(error)
                        self.logError(error, operation: "requestPermission")
                    }

                    completion(statusCode, errorInfo)
                }
            }
        } else {
            PHPhotoLibrary.requestAuthorization { status in
                DispatchQueue.main.async {
                    let statusCode = self.mapAuthorizationStatusToInt(status)
                    var errorInfo: [String: Any]? = nil

                    if status == .denied {
                        let error = self.createError(
                            code: .permissionDenied,
                            message: "相册访问权限被拒绝",
                            userInfo: ["operation": "requestPermission", "platform": "iOS_legacy"]
                        )
                        errorInfo = self.errorToInfo(error)
                        self.logError(error, operation: "requestPermission")
                    } else if status == .restricted {
                        let error = self.createError(
                            code: .permissionRestricted,
                            message: "相册访问权限受限",
                            userInfo: ["operation": "requestPermission", "platform": "iOS_legacy"]
                        )
                        errorInfo = self.errorToInfo(error)
                        self.logError(error, operation: "requestPermission")
                    }

                    completion(statusCode, errorInfo)
                }
            }
        }
        #elseif os(macOS)
        PHPhotoLibrary.requestAuthorization { status in
            DispatchQueue.main.async {
                let statusCode = self.mapAuthorizationStatusToInt(status)
                var errorInfo: [String: Any]? = nil

                if status == .denied {
                    let error = self.createError(
                        code: .permissionDenied,
                        message: "相册访问权限被拒绝",
                        userInfo: ["operation": "requestPermission", "platform": "macOS"]
                    )
                    errorInfo = self.errorToInfo(error)
                    self.logError(error, operation: "requestPermission")
                } else if status == .restricted {
                    let error = self.createError(
                        code: .permissionRestricted,
                        message: "相册访问权限受限",
                        userInfo: ["operation": "requestPermission", "platform": "macOS"]
                    )
                    errorInfo = self.errorToInfo(error)
                    self.logError(error, operation: "requestPermission")
                }

                completion(statusCode, errorInfo)
            }
        }
        #endif
    }

    /// 打开系统设置页面
    ///
    /// - Returns: 是否成功打开设置页面
    public func openSettings() -> Bool {
        return openSettingsWithCompletion { success in
            // 默认实现不需要回调处理
        }
    }

    /// 打开系统设置页面（带完成回调）
    ///
    /// - Parameter completion: 打开操作完成后的回调
    /// - Returns: 是否成功发起打开操作
    public func openSettingsWithCompletion(completion: @escaping (Bool) -> Void) -> Bool {
        #if os(iOS)
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            completion(false)
            return false
        }

        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl) { success in
                completion(success)
            }
            return true
        } else {
            completion(false)
            return false
        }
        #elseif os(macOS)
        // macOS 上打开系统偏好设置的安全与隐私面板
        let workspace = NSWorkspace.shared

        // 尝试打开新版本的系统设置
        if #available(macOS 13.0, *) {
            let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Photos")!
            let success = workspace.open(url)
            completion(success)
            return success
        } else {
            // 旧版本 macOS 的系统偏好设置
            let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Photos")!
            let success = workspace.open(url)
            completion(success)
            return success
        }
        #endif
    }

    /// 显示权限引导对话框（iOS 专用）
    ///
    /// - Parameters:
    ///   - guidance: 权限引导信息
    ///   - completion: 用户操作完成后的回调
    #if os(iOS)
    public func showPermissionGuidanceAlert(guidance: PermissionGuidance, completion: @escaping (Bool) -> Void) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            completion(false)
            return
        }

        let alert = UIAlertController(
            title: guidance.title,
            message: guidance.message,
            preferredStyle: .alert
        )

        // 添加取消按钮
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            completion(false)
        })

        // 添加主要操作按钮
        if guidance.canOpenSettings {
            alert.addAction(UIAlertAction(title: guidance.actionTitle, style: .default) { [weak self] _ in
                let success = self?.openSettings() ?? false
                completion(success)
            })
        } else {
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                completion(true)
            })
        }

        window.rootViewController?.present(alert, animated: true)
    }
    #endif

    // MARK: - 相册数据访问

    /// 获取相册中的媒体资源数量
    ///
    /// - Parameter mediaType: 媒体类型筛选 (0: 全部, 1: 图片, 2: 视频)
    /// - Returns: 媒体资源数量
    public func getAssetCount(mediaType: Int32) -> Int32 {
        let fetchOptions = PHFetchOptions()

        // 根据媒体类型设置筛选条件
        switch mediaType {
        case 1: // 图片
            fetchOptions.predicate = NSPredicate(format: "mediaType = %d", PHAssetMediaType.image.rawValue)
        case 2: // 视频
            fetchOptions.predicate = NSPredicate(format: "mediaType = %d", PHAssetMediaType.video.rawValue)
        default: // 全部
            break
        }

        let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
        return Int32(fetchResult.count)
    }

    /// 获取缩略图数据
    ///
    /// - Parameters:
    ///   - assetId: 资源标识符
    ///   - width: 目标宽度
    ///   - height: 目标高度
    ///   - completion: 完成回调，返回图片数据和可能的错误信息
    public func getThumbnailData(
        assetId: String,
        width: UInt32,
        height: UInt32,
        completion: @escaping (Data?, [String: Any]?) -> Void
    ) {
        // 根据 assetId 获取 PHAsset
        let fetchOptions = PHFetchOptions()
        fetchOptions.predicate = NSPredicate(format: "localIdentifier = %@", assetId)
        let fetchResult = PHAsset.fetchAssets(with: fetchOptions)

        guard let asset = fetchResult.firstObject else {
            let error = self.createError(
                code: .assetNotFound,
                message: "找不到指定的媒体资源",
                userInfo: [
                    "assetId": assetId,
                    "operation": "getThumbnailData"
                ]
            )
            let errorInfo = self.errorToInfo(error)
            self.logError(error, operation: "getThumbnailData")
            completion(nil, errorInfo)
            return
        }

        // 配置图片请求选项
        let options = PHImageRequestOptions()
        options.isSynchronous = false
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .exact

        let targetSize = CGSize(width: CGFloat(width), height: CGFloat(height))

        // 请求图片数据
        PHImageManager.default().requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFit,
            options: options
        ) { image, info in
            // 检查是否有错误
            if let error = info?[PHImageErrorKey] as? NSError {
                let photoError = self.createError(
                    code: .systemError,
                    message: "获取图片数据失败: \(error.localizedDescription)",
                    userInfo: [
                        "assetId": assetId,
                        "operation": "getThumbnailData",
                        "systemError": error.localizedDescription,
                        "systemCode": error.code
                    ]
                )
                let errorInfo = self.errorToInfo(photoError)
                self.logError(photoError, operation: "getThumbnailData")
                completion(nil, errorInfo)
                return
            }

            // 检查是否被取消
            if let cancelled = info?[PHImageCancelledKey] as? Bool, cancelled {
                let error = self.createError(
                    code: .cancelled,
                    message: "图片请求被取消",
                    userInfo: [
                        "assetId": assetId,
                        "operation": "getThumbnailData"
                    ]
                )
                let errorInfo = self.errorToInfo(error)
                self.logError(error, operation: "getThumbnailData")
                completion(nil, errorInfo)
                return
            }

            guard let image = image else {
                let error = self.createError(
                    code: .systemError,
                    message: "无法获取图片数据",
                    userInfo: [
                        "assetId": assetId,
                        "operation": "getThumbnailData"
                    ]
                )
                let errorInfo = self.errorToInfo(error)
                self.logError(error, operation: "getThumbnailData")
                completion(nil, errorInfo)
                return
            }

            // 将图片转换为 Data
            var imageData: Data?
            #if os(iOS)
            imageData = image.jpegData(compressionQuality: 0.8)
            #elseif os(macOS)
            if let tiffData = image.tiffRepresentation,
               let bitmapRep = NSBitmapImageRep(data: tiffData) {
                imageData = bitmapRep.representation(using: .jpeg, properties: [.compressionFactor: 0.8])
            }
            #endif

            guard let finalImageData = imageData else {
                let error = self.createError(
                    code: .unsupportedFormat,
                    message: "无法将图片转换为 JPEG 格式",
                    userInfo: [
                        "assetId": assetId,
                        "operation": "getThumbnailData"
                    ]
                )
                let errorInfo = self.errorToInfo(error)
                self.logError(error, operation: "getThumbnailData")
                completion(nil, errorInfo)
                return
            }

            completion(finalImageData, nil)
        }
    }

    // MARK: - 私有辅助方法

    /// 将 PHAuthorizationStatus 映射为整数
    ///
    /// - Parameter status: PHAuthorizationStatus
    /// - Returns: 对应的整数值
    private func mapAuthorizationStatusToInt(_ status: PHAuthorizationStatus) -> Int32 {
        switch status {
        case .notDetermined:
            return 0  // NotDetermined
        case .restricted:
            return 1  // Restricted
        case .denied:
            return 2  // Denied
        case .authorized:
            return 3  // Authorized
        case .limited:
            return 4  // Limited (iOS 14+)
        @unknown default:
            return 2  // Denied (安全默认值)
        }
    }
}

// MARK: - C 兼容的桥接函数

/// 简单的 Swift 函数，供 Rust 调用
@_cdecl("print_hello_swift")
public func print_hello_swift() {
    print("Hello from Swift!")
}

/// 初始化相册桥接模块
@_cdecl("photo_bridge_init")
public func photoBridgeInit() -> Bool {
    // 执行必要的初始化操作
    return true
}

/// 检查权限状态的 C 兼容函数
@_cdecl("photo_bridge_check_permission_status")
public func photo_bridge_check_permission_status() -> Int32 {
    return PhotoBridge.shared.checkPermissionStatus()
}

/// 获取资源数量的 C 兼容函数
@_cdecl("photo_bridge_get_asset_count")
public func photo_bridge_get_asset_count(media_type: Int32) -> Int32 {
    return PhotoBridge.shared.getAssetCount(mediaType: media_type)
}

/// 请求权限的 C 兼容函数（同步版本，用于演示）
@_cdecl("photo_bridge_request_permission_sync")
public func photo_bridge_request_permission_sync() -> Int32 {
    let semaphore = DispatchSemaphore(value: 0)
    var resultStatus: Int32 = 2 // 默认为拒绝

    PhotoBridge.shared.requestPermission { status, _ in
        resultStatus = status
        semaphore.signal()
    }

    // 等待权限请求完成，最多等待 10 秒
    let timeout = DispatchTime.now() + .seconds(10)
    let result = semaphore.wait(timeout: timeout)

    if result == .timedOut {
        print("权限请求超时")
        return 2 // 返回拒绝状态
    }

    return resultStatus
}

/// 测试函数：返回一个固定的数字，用于验证桥接功能
@_cdecl("photo_bridge_test_function")
public func photo_bridge_test_function() -> Int32 {
    return 12345
}

/// 调试函数：尝试强制获取资源数量（用于测试）
@_cdecl("photo_bridge_debug_get_count")
public func photo_bridge_debug_get_count() -> Int32 {
    // 直接尝试获取，不检查权限
    let fetchOptions = PHFetchOptions()
    let allPhotos = PHAsset.fetchAssets(with: .image, options: fetchOptions)
    print("调试：直接获取到的照片数量: \(allPhotos.count)")
    return Int32(allPhotos.count)
}

/// 打开设置页面的 C 兼容函数
@_cdecl("photo_bridge_open_settings")
public func photoBridgeOpenSettings() -> Bool {
    return PhotoBridge.shared.openSettings()
}

// MARK: - 权限请求的异步桥接

/// 权限请求回调类型（带错误信息）
public typealias PermissionCallbackWithError = @convention(c) (Int32, UnsafePointer<CChar>?) -> Void

/// 存储权限请求回调的全局变量
private var permissionCallbackWithError: PermissionCallbackWithError?

/// 请求权限的 C 兼容函数（带错误信息）
@_cdecl("photo_bridge_request_permission_with_error")
public func photoBridgeRequestPermissionWithError(callback: @escaping PermissionCallbackWithError) {
    permissionCallbackWithError = callback

    PhotoBridge.shared.requestPermission { status, errorInfo in
        var errorJson: UnsafePointer<CChar>? = nil

        if let error = errorInfo {
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: error, options: [])
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    // 创建持久的 C 字符串
                    let cString = strdup(jsonString)
                    errorJson = UnsafePointer<CChar>(cString)
                }
            } catch {
                print("Failed to serialize error info: \(error)")
            }
        }

        callback(status, errorJson)

        // 清理内存
        if let ptr = errorJson {
            free(UnsafeMutablePointer(mutating: ptr))
        }

        permissionCallbackWithError = nil
    }
}

/// 权限请求回调类型（向后兼容）
public typealias PermissionCallback = @convention(c) (Int32) -> Void

/// 存储权限请求回调的全局变量（向后兼容）
private var permissionCallback: PermissionCallback?

/// 请求权限的 C 兼容函数（向后兼容）
@_cdecl("photo_bridge_request_permission")
public func photoBridgeRequestPermission(callback: @escaping PermissionCallback) {
    permissionCallback = callback

    PhotoBridge.shared.requestPermission { status, _ in
        callback(status)
        permissionCallback = nil
    }
}

// MARK: - 缩略图数据获取的异步桥接

/// 缩略图数据回调类型（带错误信息）
public typealias ThumbnailDataCallbackWithError = @convention(c) (UnsafePointer<UInt8>?, Int32, UnsafePointer<CChar>?) -> Void

/// 获取缩略图数据的 C 兼容函数（带错误信息）
@_cdecl("photo_bridge_get_thumbnail_data_with_error")
public func photoBridgeGetThumbnailDataWithError(
    assetId: UnsafePointer<CChar>,
    width: UInt32,
    height: UInt32,
    callback: @escaping ThumbnailDataCallbackWithError
) {
    let assetIdString = String(cString: assetId)

    PhotoBridge.shared.getThumbnailData(
        assetId: assetIdString,
        width: width,
        height: height
    ) { data, errorInfo in
        var errorJson: UnsafePointer<CChar>? = nil

        if let error = errorInfo {
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: error, options: [])
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    // 创建持久的 C 字符串
                    let cString = strdup(jsonString)
                    errorJson = UnsafePointer<CChar>(cString)
                }
            } catch {
                print("Failed to serialize error info: \(error)")
            }
        }

        if let data = data {
            data.withUnsafeBytes { bytes in
                let pointer = bytes.bindMemory(to: UInt8.self).baseAddress
                callback(pointer, Int32(data.count), errorJson)
            }
        } else {
            callback(nil, 0, errorJson)
        }

        // 清理内存
        if let ptr = errorJson {
            free(UnsafeMutablePointer(mutating: ptr))
        }
    }
}

/// 缩略图数据回调类型（向后兼容）
public typealias ThumbnailDataCallback = @convention(c) (UnsafePointer<UInt8>?, Int32) -> Void

/// 获取缩略图数据的 C 兼容函数（向后兼容）
@_cdecl("photo_bridge_get_thumbnail_data")
public func photoBridgeGetThumbnailData(
    assetId: UnsafePointer<CChar>,
    width: UInt32,
    height: UInt32,
    callback: @escaping ThumbnailDataCallback
) {
    let assetIdString = String(cString: assetId)

    PhotoBridge.shared.getThumbnailData(
        assetId: assetIdString,
        width: width,
        height: height
    ) { data, _ in
        if let data = data {
            data.withUnsafeBytes { bytes in
                let pointer = bytes.bindMemory(to: UInt8.self).baseAddress
                callback(pointer, Int32(data.count))
            }
        } else {
            callback(nil, 0)
        }
    }
}

// MARK: - C 兼容接口扩展

/// C 兼容的权限请求回调类型
public typealias PermissionRequestCallback = @convention(c) (Int32, UnsafePointer<UInt8>?, Int) -> Void

/// 存储当前的权限请求回调
private var currentPermissionCallback: PermissionRequestCallback?

/// C 兼容的权限请求函数（带详细错误信息）
///
/// - Parameter callback: 权限请求完成后的回调函数
@_cdecl("photo_bridge_request_permission_detailed")
public func photo_bridge_request_permission_detailed(_ callback: @escaping PermissionRequestCallback) {
    currentPermissionCallback = callback

    PhotoBridge.shared.requestPermission { statusCode, errorInfo in
        if let errorInfo = errorInfo {
            // 有错误信息，序列化为 JSON
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: errorInfo, options: [])
                jsonData.withUnsafeBytes { bytes in
                    let pointer = bytes.bindMemory(to: UInt8.self).baseAddress
                    callback(statusCode, pointer, jsonData.count)
                }
            } catch {
                // JSON 序列化失败，发送简单错误消息
                let errorMessage = "JSON 序列化失败"
                let data = errorMessage.data(using: String.Encoding.utf8) ?? Data()
                data.withUnsafeBytes { bytes in
                    let pointer = bytes.bindMemory(to: UInt8.self).baseAddress
                    callback(statusCode, pointer, data.count)
                }
            }
        } else {
            // 没有错误，只返回状态码
            callback(statusCode, nil, 0)
        }

        // 清理回调引用
        currentPermissionCallback = nil
    }
}

// MARK: - 权限引导功能的 C 兼容桥接函数

/// 获取权限引导信息的 C 兼容函数
@_cdecl("photo_bridge_get_permission_guidance")
public func photoBridgeGetPermissionGuidance(
    statusCode: Int32,
    titleBuffer: UnsafeMutablePointer<CChar>,
    titleBufferSize: Int32,
    messageBuffer: UnsafeMutablePointer<CChar>,
    messageBufferSize: Int32,
    actionTitleBuffer: UnsafeMutablePointer<CChar>,
    actionTitleBufferSize: Int32,
    stepsBuffer: UnsafeMutablePointer<CChar>,
    stepsBufferSize: Int32
) -> Bool {
    let status = PHAuthorizationStatus(rawValue: Int(statusCode)) ?? .denied
    let guidance = PhotoBridge.shared.getPermissionGuidance(for: status)

    // 复制标题
    let titleData = guidance.title.data(using: String.Encoding.utf8) ?? Data()
    let titleLength = min(titleData.count, Int(titleBufferSize) - 1)
    titleData.withUnsafeBytes { bytes in
        memcpy(titleBuffer, bytes.baseAddress, titleLength)
    }
    titleBuffer[titleLength] = 0

    // 复制消息
    let messageData = guidance.message.data(using: String.Encoding.utf8) ?? Data()
    let messageLength = min(messageData.count, Int(messageBufferSize) - 1)
    messageData.withUnsafeBytes { bytes in
        memcpy(messageBuffer, bytes.baseAddress, messageLength)
    }
    messageBuffer[messageLength] = 0

    // 复制操作标题
    let actionTitleData = guidance.actionTitle.data(using: String.Encoding.utf8) ?? Data()
    let actionTitleLength = min(actionTitleData.count, Int(actionTitleBufferSize) - 1)
    actionTitleData.withUnsafeBytes { bytes in
        memcpy(actionTitleBuffer, bytes.baseAddress, actionTitleLength)
    }
    actionTitleBuffer[actionTitleLength] = 0

    // 复制步骤（用换行符分隔）
    let stepsString = guidance.steps.joined(separator: "\n")
    let stepsData = stepsString.data(using: String.Encoding.utf8) ?? Data()
    let stepsLength = min(stepsData.count, Int(stepsBufferSize) - 1)
    stepsData.withUnsafeBytes { bytes in
        memcpy(stepsBuffer, bytes.baseAddress, stepsLength)
    }
    stepsBuffer[stepsLength] = 0

    return guidance.canOpenSettings
}

/// 获取当前权限引导信息的 C 兼容函数
@_cdecl("photo_bridge_get_current_permission_guidance")
public func photoBridgeGetCurrentPermissionGuidance(
    titleBuffer: UnsafeMutablePointer<CChar>,
    titleBufferSize: Int32,
    messageBuffer: UnsafeMutablePointer<CChar>,
    messageBufferSize: Int32,
    actionTitleBuffer: UnsafeMutablePointer<CChar>,
    actionTitleBufferSize: Int32,
    stepsBuffer: UnsafeMutablePointer<CChar>,
    stepsBufferSize: Int32
) -> Bool {
    let currentStatus = PHPhotoLibrary.authorizationStatus()
    return photoBridgeGetPermissionGuidance(
        statusCode: Int32(currentStatus.rawValue),
        titleBuffer: titleBuffer,
        titleBufferSize: titleBufferSize,
        messageBuffer: messageBuffer,
        messageBufferSize: messageBufferSize,
        actionTitleBuffer: actionTitleBuffer,
        actionTitleBufferSize: actionTitleBufferSize,
        stepsBuffer: stepsBuffer,
        stepsBufferSize: stepsBufferSize
    )
}

/// 开始权限状态监听的 C 兼容函数
@_cdecl("photo_bridge_start_permission_monitoring")
public func photoBridgeStartPermissionMonitoring(callback: @escaping PermissionStatusChangeCallback) {
    PhotoBridge.shared.startPermissionStatusMonitoring(callback: callback)
}

/// 停止权限状态监听的 C 兼容函数
@_cdecl("photo_bridge_stop_permission_monitoring")
public func photoBridgeStopPermissionMonitoring() {
    PhotoBridge.shared.stopPermissionStatusMonitoring()
}

/// 带完成回调的设置页面打开函数
@_cdecl("photo_bridge_open_settings_with_callback")
public func photoBridgeOpenSettingsWithCallback(callback: @escaping @convention(c) (Bool) -> Void) -> Bool {
    return PhotoBridge.shared.openSettingsWithCompletion { success in
        callback(success)
    }
}

// MARK: - 同步版本的桥接函数（为 Rust 提供）

/// 同步版本的权限请求函数（带错误信息）
@_cdecl("photo_bridge_request_permission_with_error_sync")
public func photo_bridge_request_permission_with_error_sync(
    status_ptr: UnsafeMutablePointer<Int32>,
    error_json_ptr: UnsafeMutablePointer<UnsafePointer<CChar>?>
) {
    let semaphore = DispatchSemaphore(value: 0)

    PhotoBridge.shared.requestPermission { statusCode, errorInfo in
        status_ptr.pointee = statusCode

        if let errorInfo = errorInfo {
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: errorInfo, options: [])
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    let cString = strdup(jsonString)
                    error_json_ptr.pointee = UnsafePointer<CChar>(cString)
                } else {
                    error_json_ptr.pointee = nil
                }
            } catch {
                error_json_ptr.pointee = nil
            }
        } else {
            error_json_ptr.pointee = nil
        }

        semaphore.signal()
    }

    semaphore.wait()
}

/// 同步版本的缩略图获取函数（带错误信息）
@_cdecl("photo_bridge_get_thumbnail_data_with_error_sync")
public func photo_bridge_get_thumbnail_data_with_error_sync(
    asset_id: RustStr,
    width: UInt32,
    height: UInt32,
    data_ptr: UnsafeMutablePointer<UnsafePointer<UInt8>?>,
    data_len_ptr: UnsafeMutablePointer<Int32>,
    error_json_ptr: UnsafeMutablePointer<UnsafePointer<CChar>?>
) {
    let semaphore = DispatchSemaphore(value: 0)
    let assetIdString = asset_id.toString()

    PhotoBridge.shared.getThumbnailData(
        assetId: assetIdString,
        width: width,
        height: height
    ) { data, errorInfo in
        if let data = data {
            // 分配内存并复制数据
            let buffer = UnsafeMutablePointer<UInt8>.allocate(capacity: data.count)
            data.copyBytes(to: buffer, count: data.count)

            data_ptr.pointee = UnsafePointer(buffer)
            data_len_ptr.pointee = Int32(data.count)
            error_json_ptr.pointee = nil
        } else {
            data_ptr.pointee = nil
            data_len_ptr.pointee = 0

            if let errorInfo = errorInfo {
                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: errorInfo, options: [])
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        let cString = strdup(jsonString)
                        error_json_ptr.pointee = UnsafePointer<CChar>(cString)
                    } else {
                        error_json_ptr.pointee = nil
                    }
                } catch {
                    error_json_ptr.pointee = nil
                }
            } else {
                error_json_ptr.pointee = nil
            }
        }

        semaphore.signal()
    }

    semaphore.wait()
}