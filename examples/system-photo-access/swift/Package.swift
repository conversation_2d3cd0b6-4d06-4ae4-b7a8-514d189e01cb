// swift-tools-version: 5.7
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "SystemPhotoAccess",
    platforms: [
        .iOS(.v14),
        .macOS(.v11)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "SystemPhotoAccess",
            targets: ["SystemPhotoAccess"]),
    ],
    dependencies: [
        // Dependencies declare other packages that this package depends on.
        // .package(url: /* package url */, from: "1.0.0"),
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "SystemPhotoAccess",
            dependencies: [],
            path: "Sources/SystemPhotoAccess",
            publicHeadersPath: "include",
            cSettings: [
                .headerSearchPath("include"),
            ]
        ),
        .testTarget(
            name: "SystemPhotoAccessTests",
            dependencies: ["SystemPhotoAccess"],
            path: "Tests/SystemPhotoAccessTests"
        ),
    ]
)
