public func bridge_check_permission_status() -> PhotoPermissionStatus {
    __swift_bridge__$bridge_check_permission_status().intoSwiftRepr()
}
public func bridge_get_photo_assets_count() -> UInt {
    __swift_bridge__$bridge_get_photo_assets_count()
}
public func bridge_get_thumbnail_data<GenericToRustStr: ToRustStr>(_ asset_id: GenericToRustStr, _ width: UInt32, _ height: UInt32) -> RustVec<UInt8> {
    return asset_id.toRustStr({ asset_idAsRustStr in
        RustVec(ptr: __swift_bridge__$bridge_get_thumbnail_data(asset_idAsRustStr, width, height))
    })
}
public func bridge_parse_swift_error<GenericToRustStr: ToRustStr>(_ error_json: GenericToRustStr) -> PhotoError {
    return error_json.toRustStr({ error_jsonAsRustStr in
        __swift_bridge__$bridge_parse_swift_error(error_jsonAsRustStr).intoSwiftRepr()
    })
}
public func bridge_create_error_context<GenericToRustStr: ToRustStr>(_ operation: GenericToRustStr, _ file_path: GenericToRustStr, _ line_number: UInt32) -> RustString {
    return file_path.toRustStr({ file_pathAsRustStr in
        return operation.toRustStr({ operationAsRustStr in
        RustString(ptr: __swift_bridge__$bridge_create_error_context(operationAsRustStr, file_pathAsRustStr, line_number))
    })
    })
}
@_cdecl("__swift_bridge__$photo_bridge_check_permission_status")
func __swift_bridge__photo_bridge_check_permission_status () -> Int32 {
    photo_bridge_check_permission_status()
}

@_cdecl("__swift_bridge__$photo_bridge_get_asset_count")
func __swift_bridge__photo_bridge_get_asset_count (_ media_type: Int32) -> Int32 {
    photo_bridge_get_asset_count(media_type: media_type)
}

public enum PhotoPermissionStatus {
    case NotDetermined
    case Restricted
    case Denied
    case Authorized
    case Limited
}
extension PhotoPermissionStatus {
    func intoFfiRepr() -> __swift_bridge__$PhotoPermissionStatus {
        switch self {
            case PhotoPermissionStatus.NotDetermined:
                return __swift_bridge__$PhotoPermissionStatus(tag: __swift_bridge__$PhotoPermissionStatus$NotDetermined)
            case PhotoPermissionStatus.Restricted:
                return __swift_bridge__$PhotoPermissionStatus(tag: __swift_bridge__$PhotoPermissionStatus$Restricted)
            case PhotoPermissionStatus.Denied:
                return __swift_bridge__$PhotoPermissionStatus(tag: __swift_bridge__$PhotoPermissionStatus$Denied)
            case PhotoPermissionStatus.Authorized:
                return __swift_bridge__$PhotoPermissionStatus(tag: __swift_bridge__$PhotoPermissionStatus$Authorized)
            case PhotoPermissionStatus.Limited:
                return __swift_bridge__$PhotoPermissionStatus(tag: __swift_bridge__$PhotoPermissionStatus$Limited)
        }
    }
}
extension __swift_bridge__$PhotoPermissionStatus {
    func intoSwiftRepr() -> PhotoPermissionStatus {
        switch self.tag {
            case __swift_bridge__$PhotoPermissionStatus$NotDetermined:
                return PhotoPermissionStatus.NotDetermined
            case __swift_bridge__$PhotoPermissionStatus$Restricted:
                return PhotoPermissionStatus.Restricted
            case __swift_bridge__$PhotoPermissionStatus$Denied:
                return PhotoPermissionStatus.Denied
            case __swift_bridge__$PhotoPermissionStatus$Authorized:
                return PhotoPermissionStatus.Authorized
            case __swift_bridge__$PhotoPermissionStatus$Limited:
                return PhotoPermissionStatus.Limited
            default:
                fatalError("Unreachable")
        }
    }
}
extension __swift_bridge__$Option$PhotoPermissionStatus {
    @inline(__always)
    func intoSwiftRepr() -> Optional<PhotoPermissionStatus> {
        if self.is_some {
            return self.val.intoSwiftRepr()
        } else {
            return nil
        }
    }
    @inline(__always)
    static func fromSwiftRepr(_ val: Optional<PhotoPermissionStatus>) -> __swift_bridge__$Option$PhotoPermissionStatus {
        if let v = val {
            return __swift_bridge__$Option$PhotoPermissionStatus(is_some: true, val: v.intoFfiRepr())
        } else {
            return __swift_bridge__$Option$PhotoPermissionStatus(is_some: false, val: __swift_bridge__$PhotoPermissionStatus())
        }
    }
}
extension PhotoPermissionStatus: Vectorizable {
    public static func vecOfSelfNew() -> UnsafeMutableRawPointer {
        __swift_bridge__$Vec_PhotoPermissionStatus$new()
    }

    public static func vecOfSelfFree(vecPtr: UnsafeMutableRawPointer) {
        __swift_bridge__$Vec_PhotoPermissionStatus$drop(vecPtr)
    }

    public static func vecOfSelfPush(vecPtr: UnsafeMutableRawPointer, value: Self) {
        __swift_bridge__$Vec_PhotoPermissionStatus$push(vecPtr, value.intoFfiRepr())
    }

    public static func vecOfSelfPop(vecPtr: UnsafeMutableRawPointer) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_PhotoPermissionStatus$pop(vecPtr)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGet(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_PhotoPermissionStatus$get(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGetMut(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_PhotoPermissionStatus$get_mut(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfAsPtr(vecPtr: UnsafeMutableRawPointer) -> UnsafePointer<Self> {
        UnsafePointer<Self>(OpaquePointer(__swift_bridge__$Vec_PhotoPermissionStatus$as_ptr(vecPtr)))
    }

    public static func vecOfSelfLen(vecPtr: UnsafeMutableRawPointer) -> UInt {
        __swift_bridge__$Vec_PhotoPermissionStatus$len(vecPtr)
    }
}
public enum MediaType {
    case Image
    case Video
    case Audio
    case Unknown
}
extension MediaType {
    func intoFfiRepr() -> __swift_bridge__$MediaType {
        switch self {
            case MediaType.Image:
                return __swift_bridge__$MediaType(tag: __swift_bridge__$MediaType$Image)
            case MediaType.Video:
                return __swift_bridge__$MediaType(tag: __swift_bridge__$MediaType$Video)
            case MediaType.Audio:
                return __swift_bridge__$MediaType(tag: __swift_bridge__$MediaType$Audio)
            case MediaType.Unknown:
                return __swift_bridge__$MediaType(tag: __swift_bridge__$MediaType$Unknown)
        }
    }
}
extension __swift_bridge__$MediaType {
    func intoSwiftRepr() -> MediaType {
        switch self.tag {
            case __swift_bridge__$MediaType$Image:
                return MediaType.Image
            case __swift_bridge__$MediaType$Video:
                return MediaType.Video
            case __swift_bridge__$MediaType$Audio:
                return MediaType.Audio
            case __swift_bridge__$MediaType$Unknown:
                return MediaType.Unknown
            default:
                fatalError("Unreachable")
        }
    }
}
extension __swift_bridge__$Option$MediaType {
    @inline(__always)
    func intoSwiftRepr() -> Optional<MediaType> {
        if self.is_some {
            return self.val.intoSwiftRepr()
        } else {
            return nil
        }
    }
    @inline(__always)
    static func fromSwiftRepr(_ val: Optional<MediaType>) -> __swift_bridge__$Option$MediaType {
        if let v = val {
            return __swift_bridge__$Option$MediaType(is_some: true, val: v.intoFfiRepr())
        } else {
            return __swift_bridge__$Option$MediaType(is_some: false, val: __swift_bridge__$MediaType())
        }
    }
}
extension MediaType: Vectorizable {
    public static func vecOfSelfNew() -> UnsafeMutableRawPointer {
        __swift_bridge__$Vec_MediaType$new()
    }

    public static func vecOfSelfFree(vecPtr: UnsafeMutableRawPointer) {
        __swift_bridge__$Vec_MediaType$drop(vecPtr)
    }

    public static func vecOfSelfPush(vecPtr: UnsafeMutableRawPointer, value: Self) {
        __swift_bridge__$Vec_MediaType$push(vecPtr, value.intoFfiRepr())
    }

    public static func vecOfSelfPop(vecPtr: UnsafeMutableRawPointer) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_MediaType$pop(vecPtr)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGet(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_MediaType$get(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGetMut(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_MediaType$get_mut(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfAsPtr(vecPtr: UnsafeMutableRawPointer) -> UnsafePointer<Self> {
        UnsafePointer<Self>(OpaquePointer(__swift_bridge__$Vec_MediaType$as_ptr(vecPtr)))
    }

    public static func vecOfSelfLen(vecPtr: UnsafeMutableRawPointer) -> UInt {
        __swift_bridge__$Vec_MediaType$len(vecPtr)
    }
}
public enum ImageFormat {
    case JPEG
    case PNG
    case HEIC
    case GIF
    case TIFF
    case Unknown
}
extension ImageFormat {
    func intoFfiRepr() -> __swift_bridge__$ImageFormat {
        switch self {
            case ImageFormat.JPEG:
                return __swift_bridge__$ImageFormat(tag: __swift_bridge__$ImageFormat$JPEG)
            case ImageFormat.PNG:
                return __swift_bridge__$ImageFormat(tag: __swift_bridge__$ImageFormat$PNG)
            case ImageFormat.HEIC:
                return __swift_bridge__$ImageFormat(tag: __swift_bridge__$ImageFormat$HEIC)
            case ImageFormat.GIF:
                return __swift_bridge__$ImageFormat(tag: __swift_bridge__$ImageFormat$GIF)
            case ImageFormat.TIFF:
                return __swift_bridge__$ImageFormat(tag: __swift_bridge__$ImageFormat$TIFF)
            case ImageFormat.Unknown:
                return __swift_bridge__$ImageFormat(tag: __swift_bridge__$ImageFormat$Unknown)
        }
    }
}
extension __swift_bridge__$ImageFormat {
    func intoSwiftRepr() -> ImageFormat {
        switch self.tag {
            case __swift_bridge__$ImageFormat$JPEG:
                return ImageFormat.JPEG
            case __swift_bridge__$ImageFormat$PNG:
                return ImageFormat.PNG
            case __swift_bridge__$ImageFormat$HEIC:
                return ImageFormat.HEIC
            case __swift_bridge__$ImageFormat$GIF:
                return ImageFormat.GIF
            case __swift_bridge__$ImageFormat$TIFF:
                return ImageFormat.TIFF
            case __swift_bridge__$ImageFormat$Unknown:
                return ImageFormat.Unknown
            default:
                fatalError("Unreachable")
        }
    }
}
extension __swift_bridge__$Option$ImageFormat {
    @inline(__always)
    func intoSwiftRepr() -> Optional<ImageFormat> {
        if self.is_some {
            return self.val.intoSwiftRepr()
        } else {
            return nil
        }
    }
    @inline(__always)
    static func fromSwiftRepr(_ val: Optional<ImageFormat>) -> __swift_bridge__$Option$ImageFormat {
        if let v = val {
            return __swift_bridge__$Option$ImageFormat(is_some: true, val: v.intoFfiRepr())
        } else {
            return __swift_bridge__$Option$ImageFormat(is_some: false, val: __swift_bridge__$ImageFormat())
        }
    }
}
extension ImageFormat: Vectorizable {
    public static func vecOfSelfNew() -> UnsafeMutableRawPointer {
        __swift_bridge__$Vec_ImageFormat$new()
    }

    public static func vecOfSelfFree(vecPtr: UnsafeMutableRawPointer) {
        __swift_bridge__$Vec_ImageFormat$drop(vecPtr)
    }

    public static func vecOfSelfPush(vecPtr: UnsafeMutableRawPointer, value: Self) {
        __swift_bridge__$Vec_ImageFormat$push(vecPtr, value.intoFfiRepr())
    }

    public static func vecOfSelfPop(vecPtr: UnsafeMutableRawPointer) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_ImageFormat$pop(vecPtr)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGet(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_ImageFormat$get(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGetMut(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_ImageFormat$get_mut(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfAsPtr(vecPtr: UnsafeMutableRawPointer) -> UnsafePointer<Self> {
        UnsafePointer<Self>(OpaquePointer(__swift_bridge__$Vec_ImageFormat$as_ptr(vecPtr)))
    }

    public static func vecOfSelfLen(vecPtr: UnsafeMutableRawPointer) -> UInt {
        __swift_bridge__$Vec_ImageFormat$len(vecPtr)
    }
}
public enum PhotoError {
    case PermissionDenied
    case PermissionRestricted
    case AssetNotFound
    case NetworkError
    case SystemError
    case UnsupportedFormat
    case OutOfMemory
    case Cancelled
    case Timeout
}
extension PhotoError {
    func intoFfiRepr() -> __swift_bridge__$PhotoError {
        switch self {
            case PhotoError.PermissionDenied:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$PermissionDenied)
            case PhotoError.PermissionRestricted:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$PermissionRestricted)
            case PhotoError.AssetNotFound:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$AssetNotFound)
            case PhotoError.NetworkError:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$NetworkError)
            case PhotoError.SystemError:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$SystemError)
            case PhotoError.UnsupportedFormat:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$UnsupportedFormat)
            case PhotoError.OutOfMemory:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$OutOfMemory)
            case PhotoError.Cancelled:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$Cancelled)
            case PhotoError.Timeout:
                return __swift_bridge__$PhotoError(tag: __swift_bridge__$PhotoError$Timeout)
        }
    }
}
extension __swift_bridge__$PhotoError {
    func intoSwiftRepr() -> PhotoError {
        switch self.tag {
            case __swift_bridge__$PhotoError$PermissionDenied:
                return PhotoError.PermissionDenied
            case __swift_bridge__$PhotoError$PermissionRestricted:
                return PhotoError.PermissionRestricted
            case __swift_bridge__$PhotoError$AssetNotFound:
                return PhotoError.AssetNotFound
            case __swift_bridge__$PhotoError$NetworkError:
                return PhotoError.NetworkError
            case __swift_bridge__$PhotoError$SystemError:
                return PhotoError.SystemError
            case __swift_bridge__$PhotoError$UnsupportedFormat:
                return PhotoError.UnsupportedFormat
            case __swift_bridge__$PhotoError$OutOfMemory:
                return PhotoError.OutOfMemory
            case __swift_bridge__$PhotoError$Cancelled:
                return PhotoError.Cancelled
            case __swift_bridge__$PhotoError$Timeout:
                return PhotoError.Timeout
            default:
                fatalError("Unreachable")
        }
    }
}
extension __swift_bridge__$Option$PhotoError {
    @inline(__always)
    func intoSwiftRepr() -> Optional<PhotoError> {
        if self.is_some {
            return self.val.intoSwiftRepr()
        } else {
            return nil
        }
    }
    @inline(__always)
    static func fromSwiftRepr(_ val: Optional<PhotoError>) -> __swift_bridge__$Option$PhotoError {
        if let v = val {
            return __swift_bridge__$Option$PhotoError(is_some: true, val: v.intoFfiRepr())
        } else {
            return __swift_bridge__$Option$PhotoError(is_some: false, val: __swift_bridge__$PhotoError())
        }
    }
}
extension PhotoError: Vectorizable {
    public static func vecOfSelfNew() -> UnsafeMutableRawPointer {
        __swift_bridge__$Vec_PhotoError$new()
    }

    public static func vecOfSelfFree(vecPtr: UnsafeMutableRawPointer) {
        __swift_bridge__$Vec_PhotoError$drop(vecPtr)
    }

    public static func vecOfSelfPush(vecPtr: UnsafeMutableRawPointer, value: Self) {
        __swift_bridge__$Vec_PhotoError$push(vecPtr, value.intoFfiRepr())
    }

    public static func vecOfSelfPop(vecPtr: UnsafeMutableRawPointer) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_PhotoError$pop(vecPtr)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGet(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_PhotoError$get(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfGetMut(vecPtr: UnsafeMutableRawPointer, index: UInt) -> Optional<Self> {
        let maybeEnum = __swift_bridge__$Vec_PhotoError$get_mut(vecPtr, index)
        return maybeEnum.intoSwiftRepr()
    }

    public static func vecOfSelfAsPtr(vecPtr: UnsafeMutableRawPointer) -> UnsafePointer<Self> {
        UnsafePointer<Self>(OpaquePointer(__swift_bridge__$Vec_PhotoError$as_ptr(vecPtr)))
    }

    public static func vecOfSelfLen(vecPtr: UnsafeMutableRawPointer) -> UInt {
        __swift_bridge__$Vec_PhotoError$len(vecPtr)
    }
}
public struct ImageSize {
    public var width: UInt32
    public var height: UInt32

    public init(width: UInt32,height: UInt32) {
        self.width = width
        self.height = height
    }

    @inline(__always)
    func intoFfiRepr() -> __swift_bridge__$ImageSize {
        { let val = self; return __swift_bridge__$ImageSize(width: val.width, height: val.height); }()
    }
}
extension __swift_bridge__$ImageSize {
    @inline(__always)
    func intoSwiftRepr() -> ImageSize {
        { let val = self; return ImageSize(width: val.width, height: val.height); }()
    }
}
extension __swift_bridge__$Option$ImageSize {
    @inline(__always)
    func intoSwiftRepr() -> Optional<ImageSize> {
        if self.is_some {
            return self.val.intoSwiftRepr()
        } else {
            return nil
        }
    }

    @inline(__always)
    static func fromSwiftRepr(_ val: Optional<ImageSize>) -> __swift_bridge__$Option$ImageSize {
        if let v = val {
            return __swift_bridge__$Option$ImageSize(is_some: true, val: v.intoFfiRepr())
        } else {
            return __swift_bridge__$Option$ImageSize(is_some: false, val: __swift_bridge__$ImageSize())
        }
    }
}


