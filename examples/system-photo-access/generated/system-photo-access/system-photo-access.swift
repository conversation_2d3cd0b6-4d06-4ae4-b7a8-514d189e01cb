public func print_hello_rust() {
    __swift_bridge__$print_hello_rust()
}
@_cdecl("__swift_bridge__$print_hello_swift")
func __swift_bridge__print_hello_swift () {
    print_hello_swift()
}

@_cdecl("__swift_bridge__$photo_bridge_check_permission_status")
func __swift_bridge__photo_bridge_check_permission_status () -> Int32 {
    photo_bridge_check_permission_status()
}

@_cdecl("__swift_bridge__$photo_bridge_get_asset_count")
func __swift_bridge__photo_bridge_get_asset_count (_ media_type: Int32) -> Int32 {
    photo_bridge_get_asset_count(media_type: media_type)
}

@_cdecl("__swift_bridge__$photo_bridge_request_permission_sync")
func __swift_bridge__photo_bridge_request_permission_sync () -> Int32 {
    photo_bridge_request_permission_sync()
}

@_cdecl("__swift_bridge__$photo_bridge_test_function")
func __swift_bridge__photo_bridge_test_function () -> Int32 {
    photo_bridge_test_function()
}

@_cdecl("__swift_bridge__$photo_bridge_debug_get_count")
func __swift_bridge__photo_bridge_debug_get_count () -> Int32 {
    photo_bridge_debug_get_count()
}



