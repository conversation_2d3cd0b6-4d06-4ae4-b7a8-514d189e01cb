client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PackageStructure": ["<PackageStructure>"]
  "SystemPhotoAccess-arm64-apple-macosx15.0-release.module": ["<SystemPhotoAccess-arm64-apple-macosx15.0-release.module>"]
  "SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.module": ["<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.module>"]
  "SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.test": ["<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.test>"]
  "SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module": ["<SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module>"]
  "main": ["<SystemPhotoAccess-arm64-apple-macosx15.0-release.module>"]
  "test": ["<SystemPhotoAccess-arm64-apple-macosx15.0-release.module>","<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.test>","<SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module>"]
default: "main"
nodes:
  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources"]
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources"

  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources"

  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift":
    tool: test-entry-point-tool
    inputs: []
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift"]

  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList"

  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/PhotoBridgeTests.swift"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources"

  "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"

  "<SystemPhotoAccess-arm64-apple-macosx15.0-release.module>":
    tool: phony
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule"]
    outputs: ["<SystemPhotoAccess-arm64-apple-macosx15.0-release.module>"]

  "<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.module>":
    tool: phony
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessPackageTests.swiftmodule"]
    outputs: ["<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.module>"]

  "<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.test>":
    tool: phony
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.xctest/Contents/MacOS/SystemPhotoAccessPackageTests"]
    outputs: ["<SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.test>"]

  "<SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module>":
    tool: phony
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule"]
    outputs: ["<SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module>"]

  "C.SystemPhotoAccess-arm64-apple-macosx15.0-release.module":
    tool: shell
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule"]
    description: "Compiling Swift Module 'SystemPhotoAccess' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","SystemPhotoAccess","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule","-output-file-map","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/output-file-map.json","-parse-as-library","-whole-module-optimization","-num-threads","8","-c","@/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources","-I","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules","-target","arm64-apple-macosx11.0","-O","-j8","-DSWIFT_PACKAGE","-module-cache-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/SystemPhotoAccess-Swift.h","-swift-version","5","-Xcc","-I/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/include","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g"]

  "C.SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.module":
    tool: shell
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessPackageTests.swiftmodule"]
    description: "Compiling Swift Module 'SystemPhotoAccessPackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","SystemPhotoAccessPackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessPackageTests.swiftmodule","-output-file-map","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/output-file-map.json","-parse-as-library","-whole-module-optimization","-num-threads","8","-c","@/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources","-I","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules","-target","arm64-apple-macosx11.0","-O","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j8","-DSWIFT_PACKAGE","-module-cache-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/SystemPhotoAccessPackageTests-Swift.h","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g"]

  "C.SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.test":
    tool: shell
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.xctest/Contents/MacOS/SystemPhotoAccessPackageTests"]
    description: "Linking /Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.xctest/Contents/MacOS/SystemPhotoAccessPackageTests"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release","-o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.xctest/Contents/MacOS/SystemPhotoAccessPackageTests","-module-name","SystemPhotoAccessPackageTests","-Xlinker","-no_warn_duplicate_libraries","-Xlinker","-bundle","-Xlinker","-dead_strip","-Xlinker","-rpath","-Xlinker","@loader_path/../../../","@/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList","-target","arm64-apple-macosx14.0","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module":
    tool: shell
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/PhotoBridgeTests.swift","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule"]
    description: "Compiling Swift Module 'SystemPhotoAccessTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","SystemPhotoAccessTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule","-output-file-map","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/output-file-map.json","-parse-as-library","-whole-module-optimization","-num-threads","8","-c","@/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources","-I","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/Modules","-target","arm64-apple-macosx14.0","-index-store-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/index/store","-O","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j8","-DSWIFT_PACKAGE","-module-cache-path","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/ModuleCache","-parseable-output","-parse-as-library","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Package.swift","/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

