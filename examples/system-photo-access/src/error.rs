//! 错误处理模块
//!
//! 定义了系统相册访问过程中可能出现的各种错误类型。
//!
//! ## 功能特性
//!
//! - 完整的错误类型定义
//! - Swift NSError 到 Rust Error 的转换
//! - 错误信息本地化支持
//! - 错误日志记录机制
//! - 错误链追踪支持

use std::collections::HashMap;
use thiserror::Error;
use tracing::{error, info, warn};

/// 支持的语言类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Language {
    /// 中文
    Chinese,
    /// 英文
    English,
    /// 日文
    Japanese,
    /// 韩文
    Korean,
}

impl Default for Language {
    fn default() -> Self {
        Self::Chinese
    }
}

/// 错误上下文信息
#[derive(Debug, Clone, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub struct ErrorContext {
    /// 操作名称
    pub operation: String,
    /// 文件路径（如果适用）
    pub file_path: Option<String>,
    /// 行号（如果适用）
    pub line_number: Option<u32>,
    /// 额外的上下文信息
    pub additional_info: HashMap<String, String>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    pub fn new(operation: impl Into<String>) -> Self {
        Self {
            operation: operation.into(),
            file_path: None,
            line_number: None,
            additional_info: HashMap::new(),
        }
    }

    /// 添加文件信息
    pub fn with_file(mut self, file_path: impl Into<String>, line_number: u32) -> Self {
        self.file_path = Some(file_path.into());
        self.line_number = Some(line_number);
        self
    }

    /// 添加额外信息
    pub fn with_info(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.additional_info.insert(key.into(), value.into());
        self
    }
}

/// 相册访问错误类型
#[derive(Debug, Error, Clone, PartialEq, Eq)]
pub enum PhotoError {
    /// 权限被拒绝
    #[error("相册访问权限被拒绝")]
    PermissionDenied {
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 权限被拒绝（带引导信息）
    #[error("相册访问权限被拒绝: {guidance_message}")]
    PermissionDeniedWithGuidance {
        /// 引导消息
        guidance_message: String,
        /// 权限引导信息
        guidance: crate::permissions::PermissionGuidance,
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 权限受限
    #[error("相册访问权限受限")]
    PermissionRestricted {
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 权限受限（带引导信息）
    #[error("相册访问权限受限: {guidance_message}")]
    PermissionRestrictedWithGuidance {
        /// 引导消息
        guidance_message: String,
        /// 权限引导信息
        guidance: crate::permissions::PermissionGuidance,
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 资源不存在
    #[error("媒体资源不存在: {id}")]
    AssetNotFound {
        id: String,
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 网络错误
    #[error("网络访问失败: {message}")]
    NetworkError {
        message: String,
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 系统错误
    #[error("系统错误: {message}")]
    SystemError {
        message: String,
        /// 错误上下文
        context: Option<ErrorContext>,
        /// 系统错误代码
        system_code: Option<i32>,
    },

    /// 数据格式错误
    #[error("数据格式不支持: {format}")]
    UnsupportedFormat {
        format: String,
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 内存不足
    #[error("内存不足")]
    OutOfMemory {
        /// 错误上下文
        context: Option<ErrorContext>,
        /// 请求的内存大小
        requested_size: Option<usize>,
    },

    /// 操作被取消
    #[error("操作被用户取消")]
    Cancelled {
        /// 错误上下文
        context: Option<ErrorContext>,
    },

    /// 超时错误
    #[error("操作超时")]
    Timeout {
        /// 错误上下文
        context: Option<ErrorContext>,
        /// 超时时长（毫秒）
        timeout_ms: Option<u64>,
    },

    /// Swift 桥接错误
    #[error("Swift 桥接错误: {message}")]
    SwiftBridgeError {
        message: String,
        /// Swift 错误代码
        swift_code: i32,
        /// Swift 错误域
        swift_domain: Option<String>,
        /// 错误上下文
        context: Option<ErrorContext>,
    },
}

impl PhotoError {
    /// 创建系统错误
    pub fn system_error(message: impl Into<String>) -> Self {
        let error = Self::SystemError {
            message: message.into(),
            context: None,
            system_code: None,
        };
        error.log_error();
        error
    }

    /// 创建带上下文的系统错误
    pub fn system_error_with_context(
        message: impl Into<String>,
        context: ErrorContext,
        system_code: Option<i32>,
    ) -> Self {
        let error = Self::SystemError {
            message: message.into(),
            context: Some(context),
            system_code,
        };
        error.log_error();
        error
    }

    /// 创建网络错误
    pub fn network_error(message: impl Into<String>) -> Self {
        let error = Self::NetworkError {
            message: message.into(),
            context: None,
        };
        error.log_error();
        error
    }

    /// 创建带上下文的网络错误
    pub fn network_error_with_context(message: impl Into<String>, context: ErrorContext) -> Self {
        let error = Self::NetworkError {
            message: message.into(),
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建资源不存在错误
    pub fn asset_not_found(id: impl Into<String>) -> Self {
        let error = Self::AssetNotFound {
            id: id.into(),
            context: None,
        };
        error.log_error();
        error
    }

    /// 创建带上下文的资源不存在错误
    pub fn asset_not_found_with_context(id: impl Into<String>, context: ErrorContext) -> Self {
        let error = Self::AssetNotFound {
            id: id.into(),
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建不支持格式错误
    pub fn unsupported_format(format: impl Into<String>) -> Self {
        let error = Self::UnsupportedFormat {
            format: format.into(),
            context: None,
        };
        error.log_error();
        error
    }

    /// 创建带上下文的不支持格式错误
    pub fn unsupported_format_with_context(
        format: impl Into<String>,
        context: ErrorContext,
    ) -> Self {
        let error = Self::UnsupportedFormat {
            format: format.into(),
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建权限被拒绝错误
    pub fn permission_denied() -> Self {
        let error = Self::PermissionDenied { context: None };
        error.log_error();
        error
    }

    /// 创建带上下文的权限被拒绝错误
    pub fn permission_denied_with_context(context: ErrorContext) -> Self {
        let error = Self::PermissionDenied {
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建带引导信息的权限被拒绝错误
    pub fn permission_denied_with_guidance(
        guidance: crate::permissions::PermissionGuidance,
    ) -> Self {
        let error = Self::PermissionDeniedWithGuidance {
            guidance_message: guidance.message.clone(),
            guidance,
            context: None,
        };
        error.log_error();
        error
    }

    /// 创建带引导信息和上下文的权限被拒绝错误
    pub fn permission_denied_with_guidance_and_context(
        guidance: crate::permissions::PermissionGuidance,
        context: ErrorContext,
    ) -> Self {
        let error = Self::PermissionDeniedWithGuidance {
            guidance_message: guidance.message.clone(),
            guidance,
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建权限受限错误
    pub fn permission_restricted() -> Self {
        let error = Self::PermissionRestricted { context: None };
        error.log_error();
        error
    }

    /// 创建带上下文的权限受限错误
    pub fn permission_restricted_with_context(context: ErrorContext) -> Self {
        let error = Self::PermissionRestricted {
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建带引导信息的权限受限错误
    pub fn permission_restricted_with_guidance(
        guidance: crate::permissions::PermissionGuidance,
    ) -> Self {
        let error = Self::PermissionRestrictedWithGuidance {
            guidance_message: guidance.message.clone(),
            guidance,
            context: None,
        };
        error.log_error();
        error
    }

    /// 创建带引导信息和上下文的权限受限错误
    pub fn permission_restricted_with_guidance_and_context(
        guidance: crate::permissions::PermissionGuidance,
        context: ErrorContext,
    ) -> Self {
        let error = Self::PermissionRestrictedWithGuidance {
            guidance_message: guidance.message.clone(),
            guidance,
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建内存不足错误
    pub fn out_of_memory(requested_size: Option<usize>) -> Self {
        let error = Self::OutOfMemory {
            context: None,
            requested_size,
        };
        error.log_error();
        error
    }

    /// 创建带上下文的内存不足错误
    pub fn out_of_memory_with_context(
        context: ErrorContext,
        requested_size: Option<usize>,
    ) -> Self {
        let error = Self::OutOfMemory {
            context: Some(context),
            requested_size,
        };
        error.log_error();
        error
    }

    /// 创建操作取消错误
    pub fn cancelled() -> Self {
        let error = Self::Cancelled { context: None };
        error.log_error();
        error
    }

    /// 创建带上下文的操作取消错误
    pub fn cancelled_with_context(context: ErrorContext) -> Self {
        let error = Self::Cancelled {
            context: Some(context),
        };
        error.log_error();
        error
    }

    /// 创建超时错误
    pub fn timeout(timeout_ms: Option<u64>) -> Self {
        let error = Self::Timeout {
            context: None,
            timeout_ms,
        };
        error.log_error();
        error
    }

    /// 创建带上下文的超时错误
    pub fn timeout_with_context(context: ErrorContext, timeout_ms: Option<u64>) -> Self {
        let error = Self::Timeout {
            context: Some(context),
            timeout_ms,
        };
        error.log_error();
        error
    }

    /// 从 Swift NSError 创建错误
    pub fn from_swift_error(
        message: impl Into<String>,
        swift_code: i32,
        swift_domain: Option<String>,
        context: Option<ErrorContext>,
    ) -> Self {
        let error = Self::SwiftBridgeError {
            message: message.into(),
            swift_code,
            swift_domain,
            context,
        };
        error.log_error();
        error
    }

    /// 检查是否为权限相关错误
    pub fn is_permission_error(&self) -> bool {
        matches!(
            self,
            Self::PermissionDenied { .. }
                | Self::PermissionDeniedWithGuidance { .. }
                | Self::PermissionRestricted { .. }
                | Self::PermissionRestrictedWithGuidance { .. }
        )
    }

    /// 检查是否为可重试错误
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            Self::NetworkError { .. } | Self::Timeout { .. } | Self::OutOfMemory { .. }
        )
    }

    /// 记录错误日志
    pub fn log_error(&self) {
        match self {
            Self::PermissionDenied { context } => {
                error!("权限被拒绝错误: {}", self);
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::PermissionDeniedWithGuidance {
                guidance_message,
                guidance,
                context,
            } => {
                error!("权限被拒绝错误（带引导）: {}", guidance_message);
                info!(
                    "引导信息: 标题={}, 操作={}",
                    guidance.title, guidance.action_title
                );
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::PermissionRestricted { context } => {
                error!("权限受限错误: {}", self);
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::PermissionRestrictedWithGuidance {
                guidance_message,
                guidance,
                context,
            } => {
                error!("权限受限错误（带引导）: {}", guidance_message);
                info!(
                    "引导信息: 标题={}, 操作={}",
                    guidance.title, guidance.action_title
                );
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::AssetNotFound { id, context } => {
                warn!("资源不存在: ID={}", id);
                if let Some(ctx) = context {
                    warn!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::NetworkError { message, context } => {
                error!("网络错误: {}", message);
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::SystemError {
                message,
                context,
                system_code,
            } => {
                error!("系统错误: {}, 系统代码: {:?}", message, system_code);
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::UnsupportedFormat { format, context } => {
                warn!("不支持的格式: {}", format);
                if let Some(ctx) = context {
                    warn!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::OutOfMemory {
                context,
                requested_size,
            } => {
                error!("内存不足: 请求大小={:?}", requested_size);
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::Cancelled { context } => {
                info!("操作被取消");
                if let Some(ctx) = context {
                    info!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::Timeout {
                context,
                timeout_ms,
            } => {
                warn!("操作超时: 超时时长={:?}ms", timeout_ms);
                if let Some(ctx) = context {
                    warn!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
            Self::SwiftBridgeError {
                message,
                swift_code,
                swift_domain,
                context,
            } => {
                error!(
                    "Swift 桥接错误: {}, 代码: {}, 域: {:?}",
                    message, swift_code, swift_domain
                );
                if let Some(ctx) = context {
                    error!(
                        "错误上下文: 操作={}, 文件={:?}, 行号={:?}",
                        ctx.operation, ctx.file_path, ctx.line_number
                    );
                }
            }
        }
    }

    /// 获取错误代码
    pub fn error_code(&self) -> i32 {
        match self {
            Self::PermissionDenied { .. } => 1001,
            Self::PermissionDeniedWithGuidance { .. } => 1001,
            Self::PermissionRestricted { .. } => 1002,
            Self::PermissionRestrictedWithGuidance { .. } => 1002,
            Self::AssetNotFound { .. } => 2001,
            Self::NetworkError { .. } => 3001,
            Self::SystemError { .. } => 4001,
            Self::UnsupportedFormat { .. } => 5001,
            Self::OutOfMemory { .. } => 6001,
            Self::Cancelled { .. } => 7001,
            Self::Timeout { .. } => 8001,
            Self::SwiftBridgeError { .. } => 9001,
        }
    }

    /// 获取权限引导信息（如果有）
    pub fn get_permission_guidance(&self) -> Option<&crate::permissions::PermissionGuidance> {
        match self {
            Self::PermissionDeniedWithGuidance { guidance, .. } => Some(guidance),
            Self::PermissionRestrictedWithGuidance { guidance, .. } => Some(guidance),
            _ => None,
        }
    }

    /// 检查是否包含权限引导信息
    pub fn has_permission_guidance(&self) -> bool {
        self.get_permission_guidance().is_some()
    }

    /// 获取用户友好的错误描述
    pub fn user_description(&self) -> &'static str {
        self.user_description_for_language(Language::Chinese)
    }

    /// 获取指定语言的用户友好错误描述
    pub fn user_description_for_language(&self, language: Language) -> &'static str {
        match (self, language) {
            (Self::PermissionDenied { .. }, Language::Chinese) => "需要相册访问权限才能继续操作",
            (Self::PermissionDenied { .. }, Language::English) => {
                "Photo library access permission is required to continue"
            }
            (Self::PermissionDenied { .. }, Language::Japanese) => {
                "続行するには写真ライブラリへのアクセス許可が必要です"
            }
            (Self::PermissionDenied { .. }, Language::Korean) => {
                "계속하려면 사진 라이브러리 액세스 권한이 필요합니다"
            }

            (Self::PermissionDeniedWithGuidance { .. }, Language::Chinese) => {
                "需要相册访问权限才能继续操作，请按照引导步骤进行设置"
            }
            (Self::PermissionDeniedWithGuidance { .. }, Language::English) => {
                "Photo library access permission is required, please follow the guidance steps"
            }
            (Self::PermissionDeniedWithGuidance { .. }, Language::Japanese) => {
                "写真ライブラリへのアクセス許可が必要です。ガイダンス手順に従ってください"
            }
            (Self::PermissionDeniedWithGuidance { .. }, Language::Korean) => {
                "사진 라이브러리 액세스 권한이 필요합니다. 안내 단계를 따라주세요"
            }

            (Self::PermissionRestricted { .. }, Language::Chinese) => {
                "相册访问权限受限，请检查设备设置"
            }
            (Self::PermissionRestricted { .. }, Language::English) => {
                "Photo library access is restricted, please check device settings"
            }
            (Self::PermissionRestricted { .. }, Language::Japanese) => {
                "写真ライブラリへのアクセスが制限されています。デバイス設定を確認してください"
            }
            (Self::PermissionRestricted { .. }, Language::Korean) => {
                "사진 라이브러리 액세스가 제한되어 있습니다. 기기 설정을 확인하세요"
            }

            (Self::PermissionRestrictedWithGuidance { .. }, Language::Chinese) => {
                "相册访问权限受限，请按照引导步骤检查设备设置"
            }
            (Self::PermissionRestrictedWithGuidance { .. }, Language::English) => {
                "Photo library access is restricted, please follow guidance to check device settings"
            }
            (Self::PermissionRestrictedWithGuidance { .. }, Language::Japanese) => {
                "写真ライブラリへのアクセスが制限されています。ガイダンスに従ってデバイス設定を確認してください"
            }
            (Self::PermissionRestrictedWithGuidance { .. }, Language::Korean) => {
                "사진 라이브러리 액세스가 제한되어 있습니다. 안내에 따라 기기 설정을 확인하세요"
            }

            (Self::AssetNotFound { .. }, Language::Chinese) => "找不到指定的照片或视频",
            (Self::AssetNotFound { .. }, Language::English) => {
                "The specified photo or video could not be found"
            }
            (Self::AssetNotFound { .. }, Language::Japanese) => {
                "指定された写真またはビデオが見つかりません"
            }
            (Self::AssetNotFound { .. }, Language::Korean) => {
                "지정된 사진 또는 비디오를 찾을 수 없습니다"
            }

            (Self::NetworkError { .. }, Language::Chinese) => "网络连接出现问题，请检查网络设置",
            (Self::NetworkError { .. }, Language::English) => {
                "Network connection problem, please check network settings"
            }
            (Self::NetworkError { .. }, Language::Japanese) => {
                "ネットワーク接続に問題があります。ネットワーク設定を確認してください"
            }
            (Self::NetworkError { .. }, Language::Korean) => {
                "네트워크 연결에 문제가 있습니다. 네트워크 설정을 확인하세요"
            }

            (Self::SystemError { .. }, Language::Chinese) => "系统出现错误，请稍后重试",
            (Self::SystemError { .. }, Language::English) => {
                "A system error occurred, please try again later"
            }
            (Self::SystemError { .. }, Language::Japanese) => {
                "システムエラーが発生しました。しばらくしてからもう一度お試しください"
            }
            (Self::SystemError { .. }, Language::Korean) => {
                "시스템 오류가 발생했습니다. 나중에 다시 시도하세요"
            }

            (Self::UnsupportedFormat { .. }, Language::Chinese) => "不支持的文件格式",
            (Self::UnsupportedFormat { .. }, Language::English) => "Unsupported file format",
            (Self::UnsupportedFormat { .. }, Language::Japanese) => {
                "サポートされていないファイル形式"
            }
            (Self::UnsupportedFormat { .. }, Language::Korean) => "지원되지 않는 파일 형식",

            (Self::OutOfMemory { .. }, Language::Chinese) => "内存不足，请关闭其他应用后重试",
            (Self::OutOfMemory { .. }, Language::English) => {
                "Out of memory, please close other apps and try again"
            }
            (Self::OutOfMemory { .. }, Language::Japanese) => {
                "メモリ不足です。他のアプリを閉じてからもう一度お試しください"
            }
            (Self::OutOfMemory { .. }, Language::Korean) => {
                "메모리가 부족합니다. 다른 앱을 닫고 다시 시도하세요"
            }

            (Self::Cancelled { .. }, Language::Chinese) => "操作已取消",
            (Self::Cancelled { .. }, Language::English) => "Operation was cancelled",
            (Self::Cancelled { .. }, Language::Japanese) => "操作がキャンセルされました",
            (Self::Cancelled { .. }, Language::Korean) => "작업이 취소되었습니다",

            (Self::Timeout { .. }, Language::Chinese) => "操作超时，请重试",
            (Self::Timeout { .. }, Language::English) => "Operation timed out, please try again",
            (Self::Timeout { .. }, Language::Japanese) => {
                "操作がタイムアウトしました。もう一度お試しください"
            }
            (Self::Timeout { .. }, Language::Korean) => {
                "작업 시간이 초과되었습니다. 다시 시도하세요"
            }

            (Self::SwiftBridgeError { .. }, Language::Chinese) => "系统桥接错误，请稍后重试",
            (Self::SwiftBridgeError { .. }, Language::English) => {
                "System bridge error, please try again later"
            }
            (Self::SwiftBridgeError { .. }, Language::Japanese) => {
                "システムブリッジエラーです。しばらくしてからもう一度お試しください"
            }
            (Self::SwiftBridgeError { .. }, Language::Korean) => {
                "시스템 브리지 오류입니다. 나중에 다시 시도하세요"
            }
        }
    }

    /// 获取建议的解决方案
    pub fn suggested_action(&self) -> Option<&'static str> {
        self.suggested_action_for_language(Language::Chinese)
    }

    /// 获取指定语言的建议解决方案
    pub fn suggested_action_for_language(&self, language: Language) -> Option<&'static str> {
        match (self, language) {
            (Self::PermissionDenied { .. }, Language::Chinese) => {
                Some("请到设置中授权相册访问权限")
            }
            (Self::PermissionDenied { .. }, Language::English) => {
                Some("Please grant photo library access permission in Settings")
            }
            (Self::PermissionDenied { .. }, Language::Japanese) => {
                Some("設定で写真ライブラリへのアクセス許可を付与してください")
            }
            (Self::PermissionDenied { .. }, Language::Korean) => {
                Some("설정에서 사진 라이브러리 액세스 권한을 부여하세요")
            }

            (Self::PermissionRestricted { .. }, Language::Chinese) => {
                Some("请检查家长控制或企业设备管理设置")
            }
            (Self::PermissionRestricted { .. }, Language::English) => {
                Some("Please check parental controls or enterprise device management settings")
            }
            (Self::PermissionRestricted { .. }, Language::Japanese) => {
                Some("ペアレンタルコントロールまたは企業デバイス管理設定を確認してください")
            }
            (Self::PermissionRestricted { .. }, Language::Korean) => {
                Some("자녀 보호 기능 또는 기업 기기 관리 설정을 확인하세요")
            }

            (Self::NetworkError { .. }, Language::Chinese) => Some("请检查网络连接后重试"),
            (Self::NetworkError { .. }, Language::English) => {
                Some("Please check network connection and try again")
            }
            (Self::NetworkError { .. }, Language::Japanese) => {
                Some("ネットワーク接続を確認してからもう一度お試しください")
            }
            (Self::NetworkError { .. }, Language::Korean) => {
                Some("네트워크 연결을 확인하고 다시 시도하세요")
            }

            (Self::OutOfMemory { .. }, Language::Chinese) => Some("请关闭其他应用释放内存"),
            (Self::OutOfMemory { .. }, Language::English) => {
                Some("Please close other apps to free up memory")
            }
            (Self::OutOfMemory { .. }, Language::Japanese) => {
                Some("他のアプリを閉じてメモリを解放してください")
            }
            (Self::OutOfMemory { .. }, Language::Korean) => {
                Some("다른 앱을 닫아 메모리를 확보하세요")
            }

            (Self::Timeout { .. }, Language::Chinese) => Some("请检查网络连接或稍后重试"),
            (Self::Timeout { .. }, Language::English) => {
                Some("Please check network connection or try again later")
            }
            (Self::Timeout { .. }, Language::Japanese) => {
                Some("ネットワーク接続を確認するか、しばらくしてからもう一度お試しください")
            }
            (Self::Timeout { .. }, Language::Korean) => {
                Some("네트워크 연결을 확인하거나 나중에 다시 시도하세요")
            }

            _ => None,
        }
    }

    /// 获取错误上下文
    pub fn context(&self) -> Option<&ErrorContext> {
        match self {
            Self::PermissionDenied { context } => context.as_ref(),
            Self::PermissionDeniedWithGuidance { context, .. } => context.as_ref(),
            Self::PermissionRestricted { context } => context.as_ref(),
            Self::PermissionRestrictedWithGuidance { context, .. } => context.as_ref(),
            Self::AssetNotFound { context, .. } => context.as_ref(),
            Self::NetworkError { context, .. } => context.as_ref(),
            Self::SystemError { context, .. } => context.as_ref(),
            Self::UnsupportedFormat { context, .. } => context.as_ref(),
            Self::OutOfMemory { context, .. } => context.as_ref(),
            Self::Cancelled { context } => context.as_ref(),
            Self::Timeout { context, .. } => context.as_ref(),
            Self::SwiftBridgeError { context, .. } => context.as_ref(),
        }
    }
}

/// 错误结果类型别名
pub type PhotoResult<T> = Result<T, PhotoError>;

/// 从 Swift 错误代码转换为 PhotoError
impl From<i32> for PhotoError {
    fn from(code: i32) -> Self {
        match code {
            1001 => Self::PermissionDenied { context: None },
            1002 => Self::PermissionRestricted { context: None },
            2001 => Self::AssetNotFound {
                id: "unknown".to_string(),
                context: None,
            },
            3001 => Self::NetworkError {
                message: "网络错误".to_string(),
                context: None,
            },
            4001 => Self::SystemError {
                message: "系统错误".to_string(),
                context: None,
                system_code: Some(code),
            },
            5001 => Self::UnsupportedFormat {
                format: "unknown".to_string(),
                context: None,
            },
            6001 => Self::OutOfMemory {
                context: None,
                requested_size: None,
            },
            7001 => Self::Cancelled { context: None },
            8001 => Self::Timeout {
                context: None,
                timeout_ms: None,
            },
            _ => Self::SystemError {
                message: format!("未知错误代码: {}", code),
                context: None,
                system_code: Some(code),
            },
        }
    }
}

/// 转换为 Swift 错误代码
impl From<PhotoError> for i32 {
    fn from(error: PhotoError) -> Self {
        error.error_code()
    }
}

/// Swift 错误信息结构
#[derive(Debug, Clone)]
pub struct SwiftErrorInfo {
    pub code: i32,
    pub domain: Option<String>,
    pub message: String,
    pub user_info: HashMap<String, String>,
}

impl SwiftErrorInfo {
    /// 创建新的 Swift 错误信息
    pub fn new(code: i32, message: impl Into<String>) -> Self {
        Self {
            code,
            domain: None,
            message: message.into(),
            user_info: HashMap::new(),
        }
    }

    /// 设置错误域
    pub fn with_domain(mut self, domain: impl Into<String>) -> Self {
        self.domain = Some(domain.into());
        self
    }

    /// 添加用户信息
    pub fn with_user_info(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.user_info.insert(key.into(), value.into());
        self
    }
}

/// 从 Swift 错误信息转换为 PhotoError
impl From<SwiftErrorInfo> for PhotoError {
    fn from(swift_error: SwiftErrorInfo) -> Self {
        let context = ErrorContext::new("swift_error_conversion")
            .with_info("swift_code", swift_error.code.to_string())
            .with_info(
                "swift_domain",
                swift_error.domain.clone().unwrap_or_default(),
            );

        match swift_error.code {
            1001 => Self::permission_denied_with_context(context),
            1002 => Self::permission_restricted_with_context(context),
            2001 => Self::asset_not_found_with_context("unknown", context),
            3001 => Self::network_error_with_context(swift_error.message, context),
            4001 => Self::system_error_with_context(
                swift_error.message,
                context,
                Some(swift_error.code),
            ),
            5001 => Self::unsupported_format_with_context("unknown", context),
            6001 => Self::out_of_memory_with_context(context, None),
            7001 => Self::cancelled_with_context(context),
            8001 => Self::timeout_with_context(context, None),
            _ => Self::from_swift_error(
                swift_error.message,
                swift_error.code,
                swift_error.domain,
                Some(context),
            ),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = PhotoError::system_error("测试错误");
        assert!(matches!(error, PhotoError::SystemError { .. }));
        assert_eq!(error.error_code(), 4001);
    }

    #[test]
    fn test_permission_error_check() {
        assert!(PhotoError::permission_denied().is_permission_error());
        assert!(PhotoError::permission_restricted().is_permission_error());
        assert!(!PhotoError::timeout(None).is_permission_error());
    }

    #[test]
    fn test_retryable_error_check() {
        assert!(PhotoError::timeout(None).is_retryable());
        assert!(PhotoError::out_of_memory(None).is_retryable());
        assert!(!PhotoError::permission_denied().is_retryable());
    }

    #[test]
    fn test_error_code_conversion() {
        let error = PhotoError::permission_denied();
        let code: i32 = error.clone().into();
        let converted_error = PhotoError::from(code);
        assert_eq!(error.error_code(), converted_error.error_code());
    }

    #[test]
    fn test_user_descriptions() {
        let error = PhotoError::permission_denied();
        assert!(!error.user_description().is_empty());

        let action = error.suggested_action();
        assert!(action.is_some());
        assert!(!action.unwrap().is_empty());
    }

    #[test]
    fn test_error_context() {
        let context = ErrorContext::new("test_operation")
            .with_file("test.rs", 42)
            .with_info("key", "value");

        let error = PhotoError::system_error_with_context("测试错误", context.clone(), Some(500));

        assert_eq!(error.error_code(), 4001);
        assert!(error.context().is_some());

        let ctx = error.context().unwrap();
        assert_eq!(ctx.operation, "test_operation");
        assert_eq!(ctx.file_path, Some("test.rs".to_string()));
        assert_eq!(ctx.line_number, Some(42));
        assert_eq!(ctx.additional_info.get("key"), Some(&"value".to_string()));
    }

    #[test]
    fn test_localization() {
        let error = PhotoError::permission_denied();

        // 测试中文
        let chinese_desc = error.user_description_for_language(Language::Chinese);
        assert_eq!(chinese_desc, "需要相册访问权限才能继续操作");

        // 测试英文
        let english_desc = error.user_description_for_language(Language::English);
        assert_eq!(
            english_desc,
            "Photo library access permission is required to continue"
        );

        // 测试日文
        let japanese_desc = error.user_description_for_language(Language::Japanese);
        assert_eq!(
            japanese_desc,
            "続行するには写真ライブラリへのアクセス許可が必要です"
        );

        // 测试韩文
        let korean_desc = error.user_description_for_language(Language::Korean);
        assert_eq!(
            korean_desc,
            "계속하려면 사진 라이브러리 액세스 권한이 필요합니다"
        );
    }

    #[test]
    fn test_swift_error_conversion() {
        let swift_error = SwiftErrorInfo::new(1001, "Permission denied")
            .with_domain("PhotoLibraryErrorDomain")
            .with_user_info("NSLocalizedDescription", "Access denied");

        let photo_error = PhotoError::from(swift_error);
        assert!(photo_error.is_permission_error());
        assert_eq!(photo_error.error_code(), 1001);

        // 检查上下文信息
        let context = photo_error.context().unwrap();
        assert_eq!(context.operation, "swift_error_conversion");
        assert_eq!(
            context.additional_info.get("swift_code"),
            Some(&"1001".to_string())
        );
        assert_eq!(
            context.additional_info.get("swift_domain"),
            Some(&"PhotoLibraryErrorDomain".to_string())
        );
    }

    #[test]
    fn test_error_logging() {
        // 这个测试主要验证日志记录不会崩溃
        let _error = PhotoError::system_error("测试日志记录");
        // log_error 在构造函数中已经被调用

        let context = ErrorContext::new("test_logging")
            .with_file("test.rs", 100)
            .with_info("test_key", "test_value");

        let error_with_context = PhotoError::network_error_with_context("网络测试错误", context);
        // 验证错误创建成功
        assert_eq!(error_with_context.error_code(), 3001);
    }

    #[test]
    fn test_all_error_types() {
        // 测试所有错误类型的创建
        let errors = vec![
            PhotoError::permission_denied(),
            PhotoError::permission_restricted(),
            PhotoError::asset_not_found("test-id"),
            PhotoError::network_error("网络错误"),
            PhotoError::system_error("系统错误"),
            PhotoError::unsupported_format("unknown"),
            PhotoError::out_of_memory(Some(1024)),
            PhotoError::cancelled(),
            PhotoError::timeout(Some(5000)),
            PhotoError::from_swift_error("Swift错误", 9001, Some("TestDomain".to_string()), None),
        ];

        // 验证每个错误都有唯一的错误代码
        let mut codes = std::collections::HashSet::new();
        for error in &errors {
            let code = error.error_code();
            assert!(!codes.contains(&code), "重复的错误代码: {}", code);
            codes.insert(code);
        }

        // 验证每个错误都有用户描述
        for error in &errors {
            assert!(!error.user_description().is_empty());
        }
    }
}
