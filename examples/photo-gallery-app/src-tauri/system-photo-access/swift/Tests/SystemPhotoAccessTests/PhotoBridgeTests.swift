//
//  PhotoBridgeTests.swift
//  SystemPhotoAccessTests
//
//  Created by swift-bridge on 2025-01-08.
//  Copyright © 2025 swift-bridge contributors. All rights reserved.
//

import XCTest
@testable import SystemPhotoAccess

final class PhotoBridgeTests: XCTestCase {
    
    var photoBridge: PhotoBridge!
    
    override func setUpWithError() throws {
        photoBridge = PhotoBridge.shared
    }
    
    override func tearDownWithError() throws {
        photoBridge = nil
    }
    
    // MARK: - 权限管理测试
    
    func testCheckPermissionStatus() throws {
        // 测试权限状态检查
        let status = photoBridge.checkPermissionStatus()
        
        // 权限状态应该在有效范围内 (0-4)
        XCTAssertTrue(status >= 0 && status <= 4, "权限状态应该在 0-4 范围内")
    }
    
    func testPermissionStatusMapping() throws {
        // 测试权限状态映射的正确性
        // 注意：这个测试可能需要在不同的权限状态下运行
        
        let status = photoBridge.checkPermissionStatus()
        
        switch status {
        case 0:
            // NotDetermined - 尚未请求权限
            break
        case 1:
            // Restricted - 权限受限
            break
        case 2:
            // Denied - 权限被拒绝
            break
        case 3:
            // Authorized - 已授权
            break
        case 4:
            // Limited - 有限授权 (iOS 14+)
            break
        default:
            XCTFail("未知的权限状态: \(status)")
        }
    }
    
    func testOpenSettings() throws {
        // 测试打开设置页面功能
        // 注意：这个测试在模拟器中可能无法正常工作
        
        let result = photoBridge.openSettings()
        
        // 在测试环境中，我们只验证函数不会崩溃
        // 实际的设置页面打开需要在真机上测试
        XCTAssertTrue(result == true || result == false, "openSettings 应该返回布尔值")
    }
    
    // MARK: - 相册数据访问测试
    
    func testGetAssetCountAllTypes() throws {
        // 测试获取所有类型的资源数量
        let count = photoBridge.getAssetCount(mediaType: 0)
        
        // 资源数量应该是非负数
        XCTAssertGreaterThanOrEqual(count, 0, "资源数量应该是非负数")
    }
    
    func testGetAssetCountImages() throws {
        // 测试获取图片资源数量
        let count = photoBridge.getAssetCount(mediaType: 1)
        
        // 图片数量应该是非负数
        XCTAssertGreaterThanOrEqual(count, 0, "图片数量应该是非负数")
    }
    
    func testGetAssetCountVideos() throws {
        // 测试获取视频资源数量
        let count = photoBridge.getAssetCount(mediaType: 2)
        
        // 视频数量应该是非负数
        XCTAssertGreaterThanOrEqual(count, 0, "视频数量应该是非负数")
    }
    
    func testGetThumbnailDataWithInvalidAssetId() throws {
        // 测试使用无效资源 ID 获取缩略图
        let expectation = XCTestExpectation(description: "获取缩略图完成")
        
        photoBridge.getThumbnailData(
            assetId: "invalid-asset-id",
            width: 100,
            height: 100
        ) { data in
            // 无效的资源 ID 应该返回 nil
            XCTAssertNil(data, "无效的资源 ID 应该返回 nil")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    // MARK: - C 兼容函数测试
    
    func testPhotoBridgeInit() throws {
        // 测试 C 兼容的初始化函数
        let result = photoBridgeInit()
        XCTAssertTrue(result, "photoBridgeInit 应该返回 true")
    }
    
    func testPhotoBridgeCheckPermissionStatus() throws {
        // 测试 C 兼容的权限检查函数
        let status = photoBridgeCheckPermissionStatus()
        XCTAssertTrue(status >= 0 && status <= 4, "权限状态应该在 0-4 范围内")
    }
    
    func testPhotoBridgeGetAssetCount() throws {
        // 测试 C 兼容的资源数量获取函数
        let count = photoBridgeGetAssetCount(mediaType: 0)
        XCTAssertGreaterThanOrEqual(count, 0, "资源数量应该是非负数")
    }
    
    func testPhotoBridgeOpenSettings() throws {
        // 测试 C 兼容的设置页面打开函数
        let result = photoBridgeOpenSettings()
        XCTAssertTrue(result == true || result == false, "openSettings 应该返回布尔值")
    }
    
    // MARK: - 性能测试
    
    func testPermissionCheckPerformance() throws {
        // 测试权限检查的性能
        measure {
            _ = photoBridge.checkPermissionStatus()
        }
    }
    
    func testAssetCountPerformance() throws {
        // 测试资源数量获取的性能
        measure {
            _ = photoBridge.getAssetCount(mediaType: 0)
        }
    }
    
    // MARK: - 边界条件测试
    
    func testGetAssetCountWithInvalidMediaType() throws {
        // 测试使用无效媒体类型获取资源数量
        let count = photoBridge.getAssetCount(mediaType: 999)
        
        // 无效的媒体类型应该返回所有类型的数量
        XCTAssertGreaterThanOrEqual(count, 0, "无效媒体类型应该返回非负数")
    }
    
    func testGetThumbnailDataWithZeroSize() throws {
        // 测试使用零尺寸获取缩略图
        let expectation = XCTestExpectation(description: "获取缩略图完成")
        
        photoBridge.getThumbnailData(
            assetId: "test-asset-id",
            width: 0,
            height: 0
        ) { data in
            // 零尺寸应该返回 nil 或者处理为默认尺寸
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testGetThumbnailDataWithLargeSize() throws {
        // 测试使用大尺寸获取缩略图
        let expectation = XCTestExpectation(description: "获取缩略图完成")
        
        photoBridge.getThumbnailData(
            assetId: "test-asset-id",
            width: 4096,
            height: 4096
        ) { data in
            // 大尺寸应该能正常处理
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 10.0)
    }
}
