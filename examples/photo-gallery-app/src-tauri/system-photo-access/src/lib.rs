//! # 系统相册访问库
//!
//! 这个库提供了通过 swift-bridge 访问 iOS/macOS 系统相册的功能。
//!
//! ## 功能特性
//!
//! - 相册访问权限管理
//! - 相册内容读取
//! - 图片数据获取
//! - 错误处理和用户引导
//!
//! ## 使用示例
//!
//! ```rust
//! use system_photo_access::*;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), PhotoError> {
//!     // 检查权限状态
//!     let status = check_permission_status()?;
//!
//!     if status != PhotoPermissionStatus::Authorized {
//!         // 请求权限
//!         let new_status = request_permission().await?;
//!         if new_status != PhotoPermissionStatus::Authorized {
//!             return Err(PhotoError::PermissionDenied);
//!         }
//!     }
//!
//!     // 获取相册列表
//!     let query = PhotoQuery::default();
//!     let assets = get_photo_assets(query).await?;
//!
//!     println!("找到 {} 个媒体资源", assets.len());
//!     Ok(())
//! }
//! ```

// 模块声明
pub mod album;
pub mod error;
pub mod image;
pub mod permissions;
pub mod types;

// 测试时使用模拟的 Swift 函数
#[cfg(test)]
pub mod mock_swift;

// 重新导出主要类型和函数
pub use album::{get_asset_by_id, get_asset_count, get_photo_assets};
pub use error::{ErrorContext, Language, PhotoError, PhotoResult, SwiftErrorInfo};
pub use image::{
    get_image_data, get_multiple_images, get_thumbnail, ContentMode, ImageRequestOptions,
};
pub use permissions::{
    check_permission_status, ensure_photo_permission, ensure_photo_permission_with_guidance,
    get_permission_guidance, get_permission_guidance_for_status, open_settings,
    open_settings_async, request_permission, request_permission_with_guidance,
    start_permission_monitoring, stop_permission_monitoring, PermissionGuidance,
    PermissionStatusChangeCallback, PhotoPermissionStatus,
};
pub use types::{ImageData, ImageFormat, ImageSize, MediaType, PhotoAsset, PhotoQuery, SortOrder};

// swift-bridge 桥接模块
#[swift_bridge::bridge]
mod ffi {
    // 导出 Rust 函数给 Swift 使用
    extern "Rust" {
        fn print_hello_rust();
    }

    // 导入 Swift 函数供 Rust 使用
    extern "Swift" {
        fn print_hello_swift();
        fn photo_bridge_check_permission_status() -> i32;
        fn photo_bridge_get_asset_count(media_type: i32) -> i32;
        fn photo_bridge_request_permission_sync() -> i32;
        fn photo_bridge_test_function() -> i32;
        fn photo_bridge_debug_get_count() -> i32;
    }
}

// 桥接函数实现

/// 简单的 Rust 函数，供 Swift 调用
fn print_hello_rust() {
    println!("Hello from Rust!");
}

/// 检查权限状态的包装函数
pub fn check_permission_status_simple() -> i32 {
    ffi::photo_bridge_check_permission_status()
}

/// 获取资源数量的包装函数
pub fn get_asset_count_simple(media_type: i32) -> i32 {
    ffi::photo_bridge_get_asset_count(media_type)
}

/// 请求权限的包装函数
pub fn request_permission_simple() -> i32 {
    ffi::photo_bridge_request_permission_sync()
}

/// 测试函数的包装函数
pub fn test_function_simple() -> i32 {
    ffi::photo_bridge_test_function()
}

/// 调试函数：强制获取资源数量
pub fn debug_get_count_simple() -> i32 {
    ffi::photo_bridge_debug_get_count()
}

/// 调用 Swift 函数的示例
pub fn call_swift_hello() {
    ffi::print_hello_swift();
}

// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 初始化库
///
/// 这个函数应该在使用库的其他功能之前调用。
/// 它会初始化日志记录和其他必要的组件。
pub fn init() {
    // 初始化日志记录
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "system_photo_access=info");
    }

    // 初始化 tracing
    tracing_subscriber::fmt::init();

    log::info!("系统相册访问库已初始化，版本: {}", VERSION);
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_library_initialization() {
        init();
        assert_eq!(VERSION, env!("CARGO_PKG_VERSION"));
    }

    #[test]
    fn test_image_size_creation() {
        let size = ImageSize {
            width: 100,
            height: 200,
        };
        assert_eq!(size.width, 100);
        assert_eq!(size.height, 200);
    }
}
