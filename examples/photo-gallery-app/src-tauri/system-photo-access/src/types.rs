//! 基础类型定义模块
//! 
//! 定义了相册访问功能中使用的核心数据类型。

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 媒体类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MediaType {
    /// 图片
    Image,
    /// 视频
    Video,
    /// 音频
    Audio,
    /// 未知类型
    Unknown,
}

impl Default for MediaType {
    fn default() -> Self {
        Self::Image
    }
}

/// 图片格式
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ImageFormat {
    JPEG,
    PNG,
    HEIC,
    GIF,
    TIFF,
    Unknown(String),
}

impl Default for ImageFormat {
    fn default() -> Self {
        Self::JPEG
    }
}

impl ImageFormat {
    /// 从文件扩展名推断格式
    pub fn from_extension(ext: &str) -> Self {
        match ext.to_lowercase().as_str() {
            "jpg" | "jpeg" => Self::JPEG,
            "png" => Self::PNG,
            "heic" | "heif" => Self::HEIC,
            "gif" => Self::GIF,
            "tiff" | "tif" => Self::TIFF,
            _ => Self::Unknown(ext.to_string()),
        }
    }
    
    /// 获取 MIME 类型
    pub fn mime_type(&self) -> &'static str {
        match self {
            Self::JPEG => "image/jpeg",
            Self::PNG => "image/png",
            Self::HEIC => "image/heic",
            Self::GIF => "image/gif",
            Self::TIFF => "image/tiff",
            Self::Unknown(_) => "application/octet-stream",
        }
    }
}

/// 图片尺寸
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct ImageSize {
    pub width: u32,
    pub height: u32,
}

impl ImageSize {
    /// 创建新的图片尺寸
    pub fn new(width: u32, height: u32) -> Self {
        Self { width, height }
    }
    
    /// 计算宽高比
    pub fn aspect_ratio(&self) -> f64 {
        self.width as f64 / self.height as f64
    }
    
    /// 检查是否为正方形
    pub fn is_square(&self) -> bool {
        self.width == self.height
    }
    
    /// 检查是否为横向
    pub fn is_landscape(&self) -> bool {
        self.width > self.height
    }
    
    /// 检查是否为纵向
    pub fn is_portrait(&self) -> bool {
        self.width < self.height
    }
    
    /// 计算总像素数
    pub fn total_pixels(&self) -> u64 {
        self.width as u64 * self.height as u64
    }
}

/// 排序方式
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SortOrder {
    /// 按创建时间升序
    CreationDateAscending,
    /// 按创建时间降序
    CreationDateDescending,
    /// 按修改时间升序
    ModificationDateAscending,
    /// 按修改时间降序
    ModificationDateDescending,
}

impl Default for SortOrder {
    fn default() -> Self {
        Self::CreationDateDescending
    }
}

/// 媒体资源信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PhotoAsset {
    /// 唯一标识符
    pub id: String,
    /// 媒体类型
    pub media_type: MediaType,
    /// 创建时间
    pub creation_date: Option<DateTime<Utc>>,
    /// 修改时间
    pub modification_date: Option<DateTime<Utc>>,
    /// 像素宽度
    pub pixel_width: u32,
    /// 像素高度
    pub pixel_height: u32,
    /// 视频时长（秒）
    pub duration: Option<f64>,
    /// 文件大小（字节）
    pub file_size: Option<u64>,
    /// 是否为收藏
    pub is_favorite: bool,
    /// 是否在云端
    pub is_in_cloud: bool,
}

impl PhotoAsset {
    /// 获取图片尺寸
    pub fn image_size(&self) -> ImageSize {
        ImageSize::new(self.pixel_width, self.pixel_height)
    }
    
    /// 检查是否为图片
    pub fn is_image(&self) -> bool {
        self.media_type == MediaType::Image
    }
    
    /// 检查是否为视频
    pub fn is_video(&self) -> bool {
        self.media_type == MediaType::Video
    }
    
    /// 获取宽高比
    pub fn aspect_ratio(&self) -> f64 {
        self.image_size().aspect_ratio()
    }
}

/// 媒体查询选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PhotoQuery {
    /// 媒体类型筛选
    pub media_type: Option<MediaType>,
    /// 是否只包含收藏
    pub favorites_only: bool,
    /// 创建时间范围
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    /// 排序方式
    pub sort_order: SortOrder,
    /// 分页偏移
    pub offset: usize,
    /// 每页数量
    pub limit: usize,
}

impl Default for PhotoQuery {
    fn default() -> Self {
        Self {
            media_type: None,
            favorites_only: false,
            date_range: None,
            sort_order: SortOrder::default(),
            offset: 0,
            limit: 50,
        }
    }
}

impl PhotoQuery {
    /// 创建只查询图片的查询
    pub fn images_only() -> Self {
        Self {
            media_type: Some(MediaType::Image),
            ..Default::default()
        }
    }
    
    /// 创建只查询视频的查询
    pub fn videos_only() -> Self {
        Self {
            media_type: Some(MediaType::Video),
            ..Default::default()
        }
    }
    
    /// 创建只查询收藏的查询
    pub fn favorites_only() -> Self {
        Self {
            favorites_only: true,
            ..Default::default()
        }
    }
    
    /// 设置分页参数
    pub fn with_pagination(mut self, offset: usize, limit: usize) -> Self {
        self.offset = offset;
        self.limit = limit;
        self
    }
    
    /// 设置排序方式
    pub fn with_sort_order(mut self, sort_order: SortOrder) -> Self {
        self.sort_order = sort_order;
        self
    }
}

/// 图片数据
#[derive(Debug)]
pub struct ImageData {
    /// 图片二进制数据
    pub data: Vec<u8>,
    /// 图片格式
    pub format: ImageFormat,
    /// 图片尺寸
    pub size: ImageSize,
    /// 数据大小
    pub byte_size: usize,
}

impl ImageData {
    /// 创建新的图片数据
    pub fn new(data: Vec<u8>, format: ImageFormat, size: ImageSize) -> Self {
        let byte_size = data.len();
        Self {
            data,
            format,
            size,
            byte_size,
        }
    }
    
    /// 检查数据是否为空
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }
    
    /// 获取数据的引用
    pub fn as_bytes(&self) -> &[u8] {
        &self.data
    }
    
    /// 获取 MIME 类型
    pub fn mime_type(&self) -> &'static str {
        self.format.mime_type()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_image_size() {
        let size = ImageSize::new(1920, 1080);
        assert_eq!(size.width, 1920);
        assert_eq!(size.height, 1080);
        assert!(size.is_landscape());
        assert!(!size.is_portrait());
        assert!(!size.is_square());
        assert_eq!(size.total_pixels(), 1920 * 1080);
    }

    #[test]
    fn test_image_format() {
        assert_eq!(ImageFormat::from_extension("jpg"), ImageFormat::JPEG);
        assert_eq!(ImageFormat::from_extension("PNG"), ImageFormat::PNG);
        assert_eq!(ImageFormat::JPEG.mime_type(), "image/jpeg");
    }

    #[test]
    fn test_photo_query() {
        let query = PhotoQuery::images_only()
            .with_pagination(10, 20)
            .with_sort_order(SortOrder::CreationDateAscending);
        
        assert_eq!(query.media_type, Some(MediaType::Image));
        assert_eq!(query.offset, 10);
        assert_eq!(query.limit, 20);
        assert_eq!(query.sort_order, SortOrder::CreationDateAscending);
    }

    #[test]
    fn test_photo_asset() {
        let asset = PhotoAsset {
            id: "test-id".to_string(),
            media_type: MediaType::Image,
            creation_date: None,
            modification_date: None,
            pixel_width: 1920,
            pixel_height: 1080,
            duration: None,
            file_size: Some(1024 * 1024),
            is_favorite: false,
            is_in_cloud: false,
        };
        
        assert!(asset.is_image());
        assert!(!asset.is_video());
        assert_eq!(asset.image_size().width, 1920);
        assert_eq!(asset.image_size().height, 1080);
    }

    #[test]
    fn test_image_data() {
        let data = vec![0xFF, 0xD8, 0xFF]; // JPEG 头部
        let format = ImageFormat::JPEG;
        let size = ImageSize::new(100, 100);
        
        let image_data = ImageData::new(data.clone(), format, size);
        
        assert_eq!(image_data.byte_size, data.len());
        assert_eq!(image_data.as_bytes(), &data);
        assert_eq!(image_data.mime_type(), "image/jpeg");
        assert!(!image_data.is_empty());
    }
}
