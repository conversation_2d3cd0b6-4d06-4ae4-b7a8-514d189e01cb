//! 权限引导功能示例
//!
//! 这个示例展示了如何使用权限引导功能来帮助用户正确设置相册访问权限。

use system_photo_access::*;
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    system_photo_access::init();

    println!("=== 系统相册访问权限引导示例 ===\n");

    // 示例1: 检查当前权限状态并获取引导信息
    println!("1. 检查当前权限状态");
    match check_permission_status() {
        Ok(status) => {
            println!("当前权限状态: {:?}", status);
            println!("状态描述: {}", status.description());
            println!("是否有访问权限: {}", status.has_access());
            println!("是否可以请求权限: {}", status.can_request());
            println!("是否需要手动设置: {}", status.needs_manual_setup());

            // 获取对应的引导信息
            let guidance = get_permission_guidance_for_status(status);
            print_guidance(&guidance);
        }
        Err(error) => {
            println!("检查权限状态失败: {}", error);
        }
    }

    println!("\n{}\n", "=".repeat(50));

    // 示例2: 使用带引导的权限确保功能
    println!("2. 使用带引导的权限确保功能");
    match ensure_photo_permission_with_guidance().await {
        Ok(status) => {
            println!("权限确保成功: {:?}", status);
            if status.has_access() {
                println!("✅ 已获得相册访问权限，可以继续操作");
            } else {
                println!("❌ 未获得相册访问权限");
            }
        }
        Err(error) => {
            println!("权限确保失败: {}", error);

            // 如果错误包含引导信息，显示引导
            if let Some(guidance) = error.get_permission_guidance() {
                println!("\n📋 权限引导信息:");
                print_guidance(guidance);

                // 如果可以打开设置，询问用户是否要打开
                if guidance.can_open_settings {
                    println!("\n是否要打开系统设置页面？(y/n)");
                    let mut input = String::new();
                    std::io::stdin().read_line(&mut input)?;

                    if input.trim().to_lowercase() == "y" {
                        println!("正在打开系统设置...");
                        match open_settings() {
                            Ok(_) => {
                                println!("✅ 成功打开系统设置页面");
                                println!("请按照引导步骤设置权限，然后返回应用");

                                // 开始监听权限状态变化
                                start_permission_monitoring_example().await;
                            }
                            Err(error) => {
                                println!("❌ 打开设置页面失败: {}", error);
                            }
                        }
                    }
                }
            }
        }
    }

    println!("\n{}\n", "=".repeat(50));

    // 示例3: 演示权限状态监听
    println!("3. 权限状态监听示例");
    println!("开始监听权限状态变化...");
    println!("请在其他应用中修改权限设置来测试监听功能");
    println!("按 Enter 键停止监听");

    // 启动权限监听
    start_permission_monitoring(Box::new(|status| {
        println!("🔄 权限状态发生变化: {:?}", status);
        println!("   状态描述: {}", status.description());
        if status.has_access() {
            println!("   ✅ 现在可以访问相册了！");
        } else {
            println!("   ❌ 仍然无法访问相册");
        }
    }));

    // 等待用户输入
    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;

    // 停止监听
    stop_permission_monitoring();
    println!("权限状态监听已停止");

    println!("\n=== 示例结束 ===");
    Ok(())
}

/// 打印权限引导信息
fn print_guidance(guidance: &PermissionGuidance) {
    println!("📋 权限引导信息:");
    println!("   标题: {}", guidance.title);
    println!("   消息: {}", guidance.message);
    println!("   操作: {}", guidance.action_title);
    println!("   可以打开设置: {}", guidance.can_open_settings);
    println!("   操作步骤:");
    for (i, step) in guidance.steps.iter().enumerate() {
        println!("     {}. {}", i + 1, step);
    }
}

/// 权限状态监听示例
async fn start_permission_monitoring_example() {
    println!("\n🔍 开始监听权限状态变化...");
    println!("请在设置中修改权限，然后返回应用查看效果");

    // 设置一个简单的监听器
    start_permission_monitoring(Box::new(|status| {
        println!("🔄 检测到权限状态变化: {:?}", status);
        if status.has_access() {
            println!("🎉 太好了！现在已经获得相册访问权限");
            println!("可以继续使用相册功能了");
        }
    }));

    // 等待一段时间让用户有机会修改设置
    println!("等待权限状态变化... (10秒后自动停止)");
    tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;

    // 停止监听
    stop_permission_monitoring();
    println!("权限状态监听已停止");
}
