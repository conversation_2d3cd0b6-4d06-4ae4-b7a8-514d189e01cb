# 系统相册访问库

基于 swift-bridge 的 Rust 系统相册访问库，为 iOS 和 macOS 应用提供原生的相册访问能力。

## 功能特性

- ✅ **权限管理**: 完整的相册访问权限检查和请求
- ✅ **相册访问**: 高效的相册内容读取和查询
- ✅ **图片处理**: 支持原图和缩略图获取
- ✅ **类型安全**: 利用 Rust 类型系统确保 API 安全性
- ✅ **异步支持**: 完整的 async/await 支持
- ✅ **跨平台**: 支持 iOS 14.0+ 和 macOS 11.0+

## 快速开始

### 添加依赖

在你的 `Cargo.toml` 中添加：

```toml
[dependencies]
system-photo-access = { path = "path/to/system-photo-access" }
tokio = { version = "1.0", features = ["rt", "macros"] }
```

### 基础使用

```rust
use system_photo_access::*;

#[tokio::main]
async fn main() -> Result<(), PhotoError> {
    // 初始化库
    system_photo_access::init();
    
    // 检查并请求权限
    match ensure_photo_permission().await {
        Ok(status) if status.has_access() => {
            println!("获得相册访问权限");
        }
        Ok(_) => {
            println!("权限获取失败");
            return Ok(());
        }
        Err(PhotoError::PermissionDenied) => {
            println!("权限被拒绝，请到设置中手动授权");
            open_settings()?;
            return Ok(());
        }
        Err(error) => {
            println!("权限检查失败: {}", error);
            return Err(error);
        }
    }
    
    // 获取相册列表
    let query = PhotoQuery::images_only()
        .with_pagination(0, 20)
        .with_sort_order(SortOrder::CreationDateDescending);
    
    let assets = get_photo_assets(query).await?;
    println!("找到 {} 张图片", assets.len());
    
    // 获取第一张图片的缩略图
    if let Some(first_asset) = assets.first() {
        let thumbnail = get_thumbnail(
            &first_asset.id,
            ImageSize::new(200, 200)
        ).await?;
        
        println!("缩略图大小: {} 字节", thumbnail.byte_size);
    }
    
    Ok(())
}
```

## API 文档

### 权限管理

#### `check_permission_status() -> Result<PhotoPermissionStatus, PhotoError>`

检查当前相册访问权限状态。

```rust
let status = check_permission_status()?;
match status {
    PhotoPermissionStatus::Authorized => println!("已授权"),
    PhotoPermissionStatus::Denied => println!("被拒绝"),
    PhotoPermissionStatus::NotDetermined => println!("尚未请求"),
    _ => println!("其他状态"),
}
```

#### `request_permission() -> Result<PhotoPermissionStatus, PhotoError>`

异步请求相册访问权限。

```rust
let status = request_permission().await?;
if status.has_access() {
    println!("权限获取成功");
}
```

#### `ensure_photo_permission() -> Result<PhotoPermissionStatus, PhotoError>`

确保拥有相册访问权限的便利函数。

```rust
let status = ensure_photo_permission().await?;
// 自动处理权限检查和请求逻辑
```

### 相册访问

#### `get_photo_assets(query: PhotoQuery) -> Result<Vec<PhotoAsset>, PhotoError>`

获取相册中的媒体资源列表。

```rust
let query = PhotoQuery {
    media_type: Some(MediaType::Image),
    favorites_only: false,
    limit: 50,
    offset: 0,
    ..Default::default()
};

let assets = get_photo_assets(query).await?;
```

#### `get_asset_count(media_type: Option<MediaType>) -> Result<usize, PhotoError>`

获取媒体资源总数。

```rust
let total_count = get_asset_count(None).await?;
let image_count = get_asset_count(Some(MediaType::Image)).await?;
```

### 图片数据访问

#### `get_image_data(asset_id: &str, options: ImageRequestOptions) -> Result<ImageData, PhotoError>`

获取图片的二进制数据。

```rust
let options = ImageRequestOptions {
    target_size: Some(ImageSize::new(300, 300)),
    content_mode: ContentMode::AspectFit,
    quality: 0.8,
    allow_network_access: true,
};

let image_data = get_image_data("asset_id", options).await?;
```

#### `get_thumbnail(asset_id: &str, size: ImageSize) -> Result<ImageData, PhotoError>`

获取图片缩略图。

```rust
let thumbnail = get_thumbnail(
    "asset_id", 
    ImageSize::new(200, 200)
).await?;
```

## 数据类型

### `PhotoPermissionStatus`

相册访问权限状态：

- `NotDetermined`: 尚未请求权限
- `Restricted`: 权限受限（家长控制等）
- `Denied`: 用户拒绝权限
- `Authorized`: 用户授权完全访问
- `Limited`: 用户授权有限访问（iOS 14+）

### `PhotoAsset`

媒体资源信息：

```rust
pub struct PhotoAsset {
    pub id: String,
    pub media_type: MediaType,
    pub creation_date: Option<DateTime<Utc>>,
    pub pixel_width: u32,
    pub pixel_height: u32,
    pub is_favorite: bool,
    // ... 其他字段
}
```

### `PhotoQuery`

媒体查询选项：

```rust
pub struct PhotoQuery {
    pub media_type: Option<MediaType>,
    pub favorites_only: bool,
    pub sort_order: SortOrder,
    pub offset: usize,
    pub limit: usize,
    // ... 其他字段
}
```

## 错误处理

所有 API 都返回 `Result<T, PhotoError>`，其中 `PhotoError` 包含详细的错误信息：

```rust
match get_photo_assets(query).await {
    Ok(assets) => {
        // 处理成功结果
    }
    Err(PhotoError::PermissionDenied) => {
        println!("权限被拒绝");
        open_settings()?;
    }
    Err(PhotoError::NetworkError { message }) => {
        println!("网络错误: {}", message);
    }
    Err(error) => {
        println!("其他错误: {}", error);
        println!("用户描述: {}", error.user_description());
        
        if let Some(action) = error.suggested_action() {
            println!("建议操作: {}", action);
        }
    }
}
```

## 平台要求

- **iOS**: 14.0 或更高版本
- **macOS**: 11.0 或更高版本
- **Rust**: 1.70 或更高版本
- **Xcode**: 14.0 或更高版本

## 权限配置

在你的 iOS/macOS 应用的 `Info.plist` 中添加相册访问权限说明：

```xml
<key>NSPhotoLibraryUsageDescription</key>
<string>此应用需要访问您的相册以显示和管理照片</string>

<!-- iOS 14+ 可选：添加照片权限说明 -->
<key>NSPhotoLibraryAddUsageDescription</key>
<string>此应用需要访问您的相册以保存照片</string>
```

## 示例项目

查看 `examples/` 目录中的示例项目：

- `basic_usage.rs`: 基础使用示例
- `permission_flow.rs`: 权限处理流程示例

运行示例：

```bash
cargo run --example basic_usage
```

## 构建说明

1. 确保安装了 Xcode 和 Rust
2. 克隆项目并进入目录
3. 运行构建命令：

```bash
cargo build
```

## 许可证

本项目采用 MIT 或 Apache-2.0 双重许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.1.0

- 初始版本
- 基础权限管理功能
- 相册访问和图片数据获取
- 完整的错误处理机制
