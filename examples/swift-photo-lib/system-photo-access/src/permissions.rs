//! 权限管理模块
//!
//! 处理系统相册访问权限的检查、请求和管理。

use crate::error::{PhotoError, PhotoResult};
use serde::{Deserialize, Serialize};
use std::ffi::CStr;
use std::os::raw::c_char;

// 全局权限回调管理
lazy_static::lazy_static! {
    static ref PERMISSION_CALLBACK_SENDER: std::sync::Arc<std::sync::Mutex<Option<tokio::sync::oneshot::Sender<PermissionResult>>>> =
        std::sync::Arc::new(std::sync::Mutex::new(None));
}

/// 相册访问权限状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PhotoPermissionStatus {
    /// 尚未请求权限
    NotDetermined,
    /// 权限受限（家长控制等）
    Restricted,
    /// 用户拒绝权限
    Denied,
    /// 用户授权完全访问
    Authorized,
    /// 用户授权有限访问（iOS 14+）
    Limited,
}

impl PhotoPermissionStatus {
    /// 检查是否有访问权限
    pub fn has_access(&self) -> bool {
        matches!(self, Self::Authorized | Self::Limited)
    }

    /// 检查是否可以请求权限
    pub fn can_request(&self) -> bool {
        matches!(self, Self::NotDetermined)
    }

    /// 检查是否需要用户手动设置
    pub fn needs_manual_setup(&self) -> bool {
        matches!(self, Self::Denied | Self::Restricted)
    }

    /// 获取状态描述
    pub fn description(&self) -> &'static str {
        match self {
            Self::NotDetermined => "尚未请求相册访问权限",
            Self::Restricted => "相册访问权限受限",
            Self::Denied => "相册访问权限被拒绝",
            Self::Authorized => "已授权完全访问相册",
            Self::Limited => "已授权有限访问相册",
        }
    }

    /// 从整数值创建权限状态
    pub fn from_i32(value: i32) -> Self {
        match value {
            0 => Self::NotDetermined,
            1 => Self::Restricted,
            2 => Self::Denied,
            3 => Self::Authorized,
            4 => Self::Limited,
            _ => Self::Denied, // 安全默认值
        }
    }

    /// 转换为整数值
    pub fn to_i32(&self) -> i32 {
        match self {
            Self::NotDetermined => 0,
            Self::Restricted => 1,
            Self::Denied => 2,
            Self::Authorized => 3,
            Self::Limited => 4,
        }
    }
}

/// 权限请求结果
pub type PermissionResult = PhotoResult<PhotoPermissionStatus>;

/// 权限引导信息
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct PermissionGuidance {
    /// 引导标题
    pub title: String,
    /// 引导消息
    pub message: String,
    /// 操作按钮标题
    pub action_title: String,
    /// 操作步骤列表
    pub steps: Vec<String>,
    /// 是否可以打开设置页面
    pub can_open_settings: bool,
}

impl PermissionGuidance {
    /// 创建新的权限引导信息
    pub fn new(
        title: impl Into<String>,
        message: impl Into<String>,
        action_title: impl Into<String>,
        steps: Vec<String>,
        can_open_settings: bool,
    ) -> Self {
        Self {
            title: title.into(),
            message: message.into(),
            action_title: action_title.into(),
            steps,
            can_open_settings,
        }
    }

    /// 获取格式化的步骤字符串
    pub fn formatted_steps(&self) -> String {
        self.steps
            .iter()
            .enumerate()
            .map(|(i, step)| format!("{}. {}", i + 1, step))
            .collect::<Vec<_>>()
            .join("\n")
    }
}

/// 权限状态变化回调类型
pub type PermissionStatusChangeCallback = Box<dyn Fn(PhotoPermissionStatus) + Send + Sync>;

// 全局权限状态监听管理
lazy_static::lazy_static! {
    static ref PERMISSION_STATUS_CALLBACK: std::sync::Arc<std::sync::Mutex<Option<PermissionStatusChangeCallback>>> =
        std::sync::Arc::new(std::sync::Mutex::new(None));
}

// 外部 Swift 函数声明
extern "C" {
    fn photo_bridge_check_permission_status() -> i32;
    fn photo_bridge_request_permission(callback: extern "C" fn(i32, *const u8, usize));
    fn photo_bridge_open_settings() -> bool;
    fn photo_bridge_get_current_permission_guidance(
        title_buffer: *mut c_char,
        title_buffer_size: i32,
        message_buffer: *mut c_char,
        message_buffer_size: i32,
        action_title_buffer: *mut c_char,
        action_title_buffer_size: i32,
        steps_buffer: *mut c_char,
        steps_buffer_size: i32,
    ) -> bool;
    fn photo_bridge_start_permission_monitoring(callback: extern "C" fn(i32));
    fn photo_bridge_stop_permission_monitoring();
    fn photo_bridge_open_settings_with_callback(callback: extern "C" fn(bool)) -> bool;
}

/// 检查当前相册访问权限状态
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 当前权限状态
/// - `Err(PhotoError)`: 检查失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::check_permission_status;
///
/// let status = check_permission_status()?;
/// println!("当前权限状态: {:?}", status);
/// ```
pub fn check_permission_status() -> PermissionResult {
    log::debug!("检查相册访问权限状态");

    // 调用 Swift 端的权限检查函数
    let status_code = unsafe { photo_bridge_check_permission_status() };
    let status = PhotoPermissionStatus::from_i32(status_code);

    log::debug!("权限状态检查完成: {:?}", status);
    Ok(status)
}

/// 请求相册访问权限（异步）
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 用户授权后的权限状态
/// - `Err(PhotoError)`: 权限请求失败的错误信息
///
/// # 注意
/// - 如果权限已被拒绝，此函数不会再次显示权限对话框
/// - 建议在调用前先检查权限状态
///
/// # 示例
/// ```rust
/// use system_photo_access::{request_permission, PhotoPermissionStatus};
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     let status = request_permission().await?;
///
///     match status {
///         PhotoPermissionStatus::Authorized => {
///             println!("获得完全访问权限");
///         }
///         PhotoPermissionStatus::Limited => {
///             println!("获得有限访问权限");
///         }
///         _ => {
///             println!("权限请求失败");
///         }
///     }
///
///     Ok(())
/// }
/// ```
pub async fn request_permission() -> PermissionResult {
    log::info!("请求相册访问权限");

    // 首先检查当前状态
    let current_status = check_permission_status()?;

    match current_status {
        PhotoPermissionStatus::Authorized | PhotoPermissionStatus::Limited => {
            // 已有权限，直接返回
            log::debug!("权限已存在: {:?}", current_status);
            return Ok(current_status);
        }
        PhotoPermissionStatus::Denied | PhotoPermissionStatus::Restricted => {
            // 权限被拒绝或受限，无法重新请求
            log::warn!("权限被拒绝或受限: {:?}", current_status);
            return Err(match current_status {
                PhotoPermissionStatus::Denied => PhotoError::permission_denied(),
                PhotoPermissionStatus::Restricted => PhotoError::permission_restricted(),
                _ => unreachable!(),
            });
        }
        PhotoPermissionStatus::NotDetermined => {
            // 可以请求权限
            log::debug!("开始请求权限");
        }
    }

    // 实现异步权限请求
    let result = request_permission_async().await;

    match result {
        Ok(status) => {
            log::info!("权限请求完成: {:?}", status);
            Ok(status)
        }
        Err(error) => {
            log::error!("权限请求失败: {}", error);
            Err(error)
        }
    }
}

/// 请求相册访问权限（回调版本）
///
/// # 参数
/// - `callback`: 权限请求完成后的回调函数
///
/// # 示例
/// ```rust
/// use system_photo_access::request_permission_with_callback;
///
/// request_permission_with_callback(|result| {
///     match result {
///         Ok(status) => println!("权限状态: {:?}", status),
///         Err(error) => println!("权限请求失败: {}", error),
///     }
/// });
/// ```
pub fn request_permission_with_callback<F>(callback: F)
where
    F: FnOnce(PermissionResult) + Send + 'static,
{
    log::debug!("使用回调方式请求权限");

    // 在异步运行时中执行权限请求
    tokio::spawn(async move {
        let result = request_permission().await;
        callback(result);
    });
}

/// 获取当前权限状态的引导信息
///
/// # 返回值
/// - `Ok(PermissionGuidance)`: 权限引导信息
/// - `Err(PhotoError)`: 获取失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::get_permission_guidance;
///
/// let guidance = get_permission_guidance()?;
/// println!("引导标题: {}", guidance.title);
/// println!("引导消息: {}", guidance.message);
/// for (i, step) in guidance.steps.iter().enumerate() {
///     println!("{}. {}", i + 1, step);
/// }
/// ```
pub fn get_permission_guidance() -> PhotoResult<PermissionGuidance> {
    log::debug!("获取权限引导信息");

    const BUFFER_SIZE: i32 = 1024;
    let mut title_buffer = vec![0u8; BUFFER_SIZE as usize];
    let mut message_buffer = vec![0u8; BUFFER_SIZE as usize];
    let mut action_title_buffer = vec![0u8; BUFFER_SIZE as usize];
    let mut steps_buffer = vec![0u8; BUFFER_SIZE as usize * 4]; // 步骤可能较长

    let can_open_settings = unsafe {
        photo_bridge_get_current_permission_guidance(
            title_buffer.as_mut_ptr() as *mut c_char,
            BUFFER_SIZE,
            message_buffer.as_mut_ptr() as *mut c_char,
            BUFFER_SIZE,
            action_title_buffer.as_mut_ptr() as *mut c_char,
            BUFFER_SIZE,
            steps_buffer.as_mut_ptr() as *mut c_char,
            BUFFER_SIZE * 4,
        )
    };

    // 转换 C 字符串为 Rust 字符串
    let title = unsafe {
        CStr::from_ptr(title_buffer.as_ptr() as *const c_char)
            .to_string_lossy()
            .into_owned()
    };

    let message = unsafe {
        CStr::from_ptr(message_buffer.as_ptr() as *const c_char)
            .to_string_lossy()
            .into_owned()
    };

    let action_title = unsafe {
        CStr::from_ptr(action_title_buffer.as_ptr() as *const c_char)
            .to_string_lossy()
            .into_owned()
    };

    let steps_string = unsafe {
        CStr::from_ptr(steps_buffer.as_ptr() as *const c_char)
            .to_string_lossy()
            .into_owned()
    };

    // 解析步骤字符串（用换行符分隔）
    let steps: Vec<String> = steps_string
        .split('\n')
        .filter(|s| !s.trim().is_empty())
        .map(|s| s.trim().to_string())
        .collect();

    let guidance = PermissionGuidance::new(title, message, action_title, steps, can_open_settings);

    log::debug!("权限引导信息获取完成: {:?}", guidance);
    Ok(guidance)
}

/// 获取指定权限状态的引导信息
///
/// # 参数
/// - `status`: 权限状态
///
/// # 返回值
/// - `PermissionGuidance`: 对应的权限引导信息
///
/// # 示例
/// ```rust
/// use system_photo_access::{get_permission_guidance_for_status, PhotoPermissionStatus};
///
/// let guidance = get_permission_guidance_for_status(PhotoPermissionStatus::Denied);
/// println!("拒绝状态的引导: {}", guidance.message);
/// ```
pub fn get_permission_guidance_for_status(status: PhotoPermissionStatus) -> PermissionGuidance {
    match status {
        PhotoPermissionStatus::NotDetermined => PermissionGuidance::new(
            "需要相册访问权限",
            "为了让您能够选择和管理照片，我们需要访问您的相册。",
            "授权访问",
            vec![
                "点击\"授权访问\"按钮".to_string(),
                "在弹出的权限对话框中选择\"允许访问所有照片\"或\"选择照片\"".to_string(),
                "完成授权后即可开始使用相册功能".to_string(),
            ],
            false,
        ),
        PhotoPermissionStatus::Denied => PermissionGuidance::new(
            "相册访问权限被拒绝",
            "您之前拒绝了相册访问权限。要使用相册功能，请在设置中重新开启权限。",
            "前往设置",
            vec![
                "点击\"前往设置\"按钮打开系统设置".to_string(),
                "找到并点击当前应用".to_string(),
                "点击\"照片\"选项".to_string(),
                "选择\"所有照片\"或\"选定的照片\"".to_string(),
            ],
            true,
        ),
        PhotoPermissionStatus::Restricted => PermissionGuidance::new(
            "相册访问权限受限",
            "由于设备限制（如家长控制），无法访问相册。请联系设备管理员或检查设备设置。",
            "查看设置",
            vec![
                "检查设备是否启用了家长控制或企业管理".to_string(),
                "联系设备管理员获取权限".to_string(),
                "或在设备设置中调整限制选项".to_string(),
            ],
            true,
        ),
        PhotoPermissionStatus::Authorized => PermissionGuidance::new(
            "已获得完全访问权限",
            "您已授权应用访问所有照片，可以正常使用相册功能。",
            "继续使用",
            vec!["您可以正常使用所有相册功能".to_string()],
            false,
        ),
        PhotoPermissionStatus::Limited => PermissionGuidance::new(
            "已获得有限访问权限",
            "您已授权应用访问选定的照片。如需访问更多照片，可以在设置中调整权限。",
            "管理权限",
            vec![
                "点击\"管理权限\"可以选择更多照片".to_string(),
                "或前往设置中将权限改为\"所有照片\"".to_string(),
            ],
            true,
        ),
    }
}

/// 打开系统设置页面以便用户手动授权
///
/// # 返回值
/// - `Ok(())`: 成功打开设置页面
/// - `Err(PhotoError)`: 打开失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::open_settings;
///
/// if let Err(error) = open_settings() {
///     println!("无法打开设置页面: {}", error);
/// }
/// ```
pub fn open_settings() -> PhotoResult<()> {
    log::info!("尝试打开系统设置页面");

    // 调用 Swift 端的设置页面打开函数
    let success = unsafe { photo_bridge_open_settings() };

    if success {
        log::info!("成功打开设置页面");
        Ok(())
    } else {
        let error = PhotoError::system_error("无法打开设置页面");
        log::error!("打开设置页面失败: {}", error);
        Err(error)
    }
}

/// 打开系统设置页面（异步版本，带完成回调）
///
/// # 返回值
/// - `Ok(bool)`: 是否成功打开设置页面
/// - `Err(PhotoError)`: 打开失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::open_settings_async;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     let success = open_settings_async().await?;
///     if success {
///         println!("成功打开设置页面");
///     } else {
///         println!("打开设置页面失败");
///     }
///     Ok(())
/// }
/// ```
pub async fn open_settings_async() -> PhotoResult<bool> {
    use tokio::sync::oneshot;

    log::info!("异步打开系统设置页面");

    let (_sender, receiver) = oneshot::channel::<bool>();

    // 定义回调函数
    extern "C" fn settings_callback(_success: bool) {
        // 这里需要一个全局的 sender 存储机制
        // 为了简化，我们使用同步版本
    }

    // 调用 Swift 端的异步设置页面打开函数
    let initial_success = unsafe { photo_bridge_open_settings_with_callback(settings_callback) };

    if initial_success {
        // 等待回调结果，设置超时
        match tokio::time::timeout(tokio::time::Duration::from_secs(5), receiver).await {
            Ok(Ok(success)) => {
                log::info!("设置页面打开操作完成: {}", success);
                Ok(success)
            }
            Ok(Err(_)) => {
                log::error!("设置页面打开回调通道错误");
                Err(PhotoError::system_error("设置页面打开回调通道错误"))
            }
            Err(_) => {
                log::warn!("设置页面打开操作超时，假设成功");
                Ok(true) // 超时时假设成功
            }
        }
    } else {
        let error = PhotoError::system_error("无法发起设置页面打开操作");
        log::error!("设置页面打开失败: {}", error);
        Err(error)
    }
}

/// 开始权限状态监听
///
/// # 参数
/// - `callback`: 权限状态变化时的回调函数
///
/// # 示例
/// ```rust
/// use system_photo_access::{start_permission_monitoring, PhotoPermissionStatus};
///
/// start_permission_monitoring(Box::new(|status| {
///     println!("权限状态变化: {:?}", status);
/// }));
/// ```
pub fn start_permission_monitoring<F>(callback: F)
where
    F: Fn(PhotoPermissionStatus) + Send + Sync + 'static,
{
    log::info!("开始权限状态监听");

    // 存储回调函数
    {
        let mut global_callback = PERMISSION_STATUS_CALLBACK.lock().unwrap();
        *global_callback = Some(Box::new(callback));
    }

    // 启动 Swift 端的监听
    unsafe {
        photo_bridge_start_permission_monitoring(permission_status_change_callback);
    }

    log::debug!("权限状态监听已启动");
}

/// 停止权限状态监听
///
/// # 示例
/// ```rust
/// use system_photo_access::stop_permission_monitoring;
///
/// stop_permission_monitoring();
/// ```
pub fn stop_permission_monitoring() {
    log::info!("停止权限状态监听");

    // 清除回调函数
    {
        let mut global_callback = PERMISSION_STATUS_CALLBACK.lock().unwrap();
        *global_callback = None;
    }

    // 停止 Swift 端的监听
    unsafe {
        photo_bridge_stop_permission_monitoring();
    }

    log::debug!("权限状态监听已停止");
}

/// 权限状态变化回调函数
extern "C" fn permission_status_change_callback(status_code: i32) {
    log::debug!("收到权限状态变化回调: status={}", status_code);

    let status = PhotoPermissionStatus::from_i32(status_code);

    // 调用用户注册的回调函数
    if let Some(callback) = PERMISSION_STATUS_CALLBACK.lock().unwrap().as_ref() {
        callback(status);
    } else {
        log::warn!("权限状态变化回调函数未注册");
    }
}

/// 带权限引导的权限请求
///
/// 这个函数会在权限被拒绝时自动提供引导信息。
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 权限状态
/// - `Err(PhotoError)`: 权限请求失败，错误中包含引导信息
///
/// # 示例
/// ```rust
/// use system_photo_access::request_permission_with_guidance;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     match request_permission_with_guidance().await {
///         Ok(status) => {
///             println!("权限状态: {:?}", status);
///         }
///         Err(error) => {
///             println!("权限请求失败: {}", error);
///             // 错误中包含引导信息
///         }
///     }
///     Ok(())
/// }
/// ```
pub async fn request_permission_with_guidance() -> PermissionResult {
    log::info!("带引导的权限请求");

    // 首先尝试正常的权限请求
    match request_permission().await {
        Ok(status) => {
            log::info!("权限请求成功: {:?}", status);
            Ok(status)
        }
        Err(error) => {
            log::warn!("权限请求失败: {}", error);

            // 获取当前状态的引导信息
            let current_status = check_permission_status().unwrap_or(PhotoPermissionStatus::Denied);
            let guidance = get_permission_guidance_for_status(current_status);

            // 创建包含引导信息的错误
            let guidance_error = match current_status {
                PhotoPermissionStatus::Denied => {
                    PhotoError::permission_denied_with_guidance(guidance)
                }
                PhotoPermissionStatus::Restricted => {
                    PhotoError::permission_restricted_with_guidance(guidance)
                }
                _ => error,
            };

            Err(guidance_error)
        }
    }
}

/// 确保拥有相册访问权限（带引导）
///
/// 这是一个便利函数，会自动检查权限状态并在需要时请求权限。
/// 如果权限被拒绝，会提供详细的引导信息。
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 最终的权限状态
/// - `Err(PhotoError)`: 权限获取失败的错误信息（包含引导）
///
/// # 示例
/// ```rust
/// use system_photo_access::ensure_photo_permission_with_guidance;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     match ensure_photo_permission_with_guidance().await {
///         Ok(status) if status.has_access() => {
///             println!("成功获得相册访问权限");
///             // 继续访问相册
///         }
///         Ok(_) => {
///             println!("权限获取失败");
///         }
///         Err(error) => {
///             println!("权限检查失败: {}", error);
///             // 错误中包含引导信息
///         }
///     }
///
///     Ok(())
/// }
/// ```
pub async fn ensure_photo_permission_with_guidance() -> PermissionResult {
    log::debug!("确保拥有相册访问权限（带引导）");

    // 检查当前权限状态
    let current_status = check_permission_status()?;

    match current_status {
        PhotoPermissionStatus::Authorized | PhotoPermissionStatus::Limited => {
            // 已有权限，直接返回
            log::debug!("已有相册访问权限: {:?}", current_status);
            Ok(current_status)
        }
        PhotoPermissionStatus::NotDetermined => {
            // 尚未请求，发起权限请求
            log::debug!("尚未请求权限，开始请求");
            request_permission_with_guidance().await
        }
        PhotoPermissionStatus::Denied => {
            // 被拒绝，返回带引导的错误
            log::warn!("相册访问权限被拒绝");
            let guidance = get_permission_guidance_for_status(current_status);
            Err(PhotoError::permission_denied_with_guidance(guidance))
        }
        PhotoPermissionStatus::Restricted => {
            // 受限制，返回带引导的错误
            log::warn!("相册访问权限受限");
            let guidance = get_permission_guidance_for_status(current_status);
            Err(PhotoError::permission_restricted_with_guidance(guidance))
        }
    }
}

/// 确保拥有相册访问权限
///
/// 这是一个便利函数，会自动检查权限状态并在需要时请求权限。
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 最终的权限状态
/// - `Err(PhotoError)`: 权限获取失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::ensure_photo_permission;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     match ensure_photo_permission().await {
///         Ok(status) if status.has_access() => {
///             println!("成功获得相册访问权限");
///             // 继续访问相册
///         }
///         Ok(_) => {
///             println!("权限获取失败");
///         }
///         Err(error) => {
///             println!("权限检查失败: {}", error);
///         }
///     }
///
///     Ok(())
/// }
/// ```
pub async fn ensure_photo_permission() -> PermissionResult {
    log::debug!("确保拥有相册访问权限");

    // 检查当前权限状态
    let current_status = check_permission_status()?;

    match current_status {
        PhotoPermissionStatus::Authorized | PhotoPermissionStatus::Limited => {
            // 已有权限，直接返回
            log::debug!("已有相册访问权限: {:?}", current_status);
            Ok(current_status)
        }
        PhotoPermissionStatus::NotDetermined => {
            // 尚未请求，发起权限请求
            log::debug!("尚未请求权限，开始请求");
            request_permission().await
        }
        PhotoPermissionStatus::Denied => {
            // 被拒绝，返回错误
            log::warn!("相册访问权限被拒绝");
            Err(PhotoError::permission_denied())
        }
        PhotoPermissionStatus::Restricted => {
            // 受限制，返回错误
            log::warn!("相册访问权限受限");
            Err(PhotoError::permission_restricted())
        }
    }
}

/// 权限请求回调函数
extern "C" fn permission_callback(status_code: i32, error_data: *const u8, error_len: usize) {
    log::debug!(
        "收到权限请求回调: status={}, error_len={}",
        status_code,
        error_len
    );

    // 从全局存储中获取 sender
    if let Some(sender) = PERMISSION_CALLBACK_SENDER.lock().unwrap().take() {
        let result = if error_len > 0 && !error_data.is_null() {
            // 有错误信息
            let error_slice = unsafe { std::slice::from_raw_parts(error_data, error_len) };
            let error_str = String::from_utf8_lossy(error_slice);
            log::warn!("权限请求返回错误: {}", error_str);

            let status = PhotoPermissionStatus::from_i32(status_code);
            match status {
                PhotoPermissionStatus::Denied => Err(PhotoError::permission_denied()),
                PhotoPermissionStatus::Restricted => Err(PhotoError::permission_restricted()),
                _ => Err(PhotoError::system_error(&format!(
                    "权限请求失败: {}",
                    error_str
                ))),
            }
        } else {
            // 成功获取权限状态
            let status = PhotoPermissionStatus::from_i32(status_code);
            log::debug!("权限请求成功: {:?}", status);
            Ok(status)
        };

        if let Err(_) = sender.send(result) {
            log::error!("无法发送权限请求结果");
        }
    } else {
        log::error!("权限回调 sender 已被使用或不存在");
    }
}

/// 异步权限请求的内部实现
///
/// 这个函数使用 Swift 回调机制实现真正的异步权限请求
async fn request_permission_async() -> PermissionResult {
    use tokio::sync::oneshot;

    log::debug!("开始异步权限请求");

    // 创建一个 oneshot channel 用于接收回调结果
    let (sender, receiver) = oneshot::channel::<PermissionResult>();

    // 存储 sender 到全局变量
    {
        let mut global_sender = PERMISSION_CALLBACK_SENDER.lock().unwrap();
        if global_sender.is_some() {
            log::warn!("已有权限请求正在进行中");
            return Err(PhotoError::system_error("已有权限请求正在进行中"));
        }
        *global_sender = Some(sender);
    }

    // 调用 Swift 端的权限请求函数
    unsafe {
        photo_bridge_request_permission(permission_callback);
    }

    // 等待回调结果，设置超时
    match tokio::time::timeout(tokio::time::Duration::from_secs(30), receiver).await {
        Ok(Ok(result)) => {
            log::debug!("异步权限请求完成");
            result
        }
        Ok(Err(_)) => {
            log::error!("权限请求回调通道错误");
            // 清理全局 sender
            *PERMISSION_CALLBACK_SENDER.lock().unwrap() = None;
            Err(PhotoError::system_error("权限请求回调通道错误"))
        }
        Err(_) => {
            log::error!("权限请求超时");
            // 清理全局 sender
            *PERMISSION_CALLBACK_SENDER.lock().unwrap() = None;
            Err(PhotoError::system_error("权限请求超时"))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_status_methods() {
        assert!(PhotoPermissionStatus::Authorized.has_access());
        assert!(PhotoPermissionStatus::Limited.has_access());
        assert!(!PhotoPermissionStatus::Denied.has_access());

        assert!(PhotoPermissionStatus::NotDetermined.can_request());
        assert!(!PhotoPermissionStatus::Denied.can_request());

        assert!(PhotoPermissionStatus::Denied.needs_manual_setup());
        assert!(PhotoPermissionStatus::Restricted.needs_manual_setup());
        assert!(!PhotoPermissionStatus::Authorized.needs_manual_setup());
    }

    #[test]
    fn test_permission_status_description() {
        let status = PhotoPermissionStatus::Authorized;
        assert!(!status.description().is_empty());
    }

    #[test]
    fn test_check_permission_status() {
        let result = check_permission_status();
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_request_permission() {
        let result = request_permission().await;
        // 在测试环境中，权限请求可能会超时或失败，这是正常的
        match result {
            Ok(status) => {
                println!("权限请求成功: {:?}", status);
            }
            Err(error) => {
                println!("权限请求失败: {}", error);
                // 验证错误类型是预期的
                assert!(matches!(
                    error,
                    PhotoError::PermissionDenied { .. }
                        | PhotoError::PermissionRestricted { .. }
                        | PhotoError::SystemError { .. }
                ));
            }
        }
    }

    #[tokio::test]
    async fn test_ensure_photo_permission() {
        let result = ensure_photo_permission().await;
        // 在测试环境中，权限确保可能会失败，这是正常的
        match result {
            Ok(status) => {
                println!("确保权限成功: {:?}", status);
            }
            Err(error) => {
                println!("确保权限失败: {}", error);
                // 验证错误类型是预期的
                assert!(matches!(
                    error,
                    PhotoError::PermissionDenied { .. }
                        | PhotoError::PermissionRestricted { .. }
                        | PhotoError::SystemError { .. }
                ));
            }
        }
    }

    #[test]
    fn test_open_settings() {
        let result = open_settings();
        assert!(result.is_ok());
    }
}
