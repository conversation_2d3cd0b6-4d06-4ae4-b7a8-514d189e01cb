//! 模拟 Swift 函数实现
//!
//! 这个模块提供了 Swift 桥接函数的模拟实现，
//! 用于在没有真正 Swift 库的情况下进行测试。

/// 模拟的权限检查函数
#[no_mangle]
pub extern "C" fn photo_bridge_check_permission_status() -> i32 {
    // 模拟返回 "未确定" 状态
    0
}

/// 模拟的权限请求函数
#[no_mangle]
pub extern "C" fn photo_bridge_request_permission(callback: extern "C" fn(i32, *const u8, usize)) {
    // 模拟权限请求，直接调用回调返回 "已授权" 状态
    // 使用正确的签名：status_code, error_data, error_len
    callback(3, std::ptr::null(), 0);
}

/// 模拟的设置页面打开函数
#[no_mangle]
pub extern "C" fn photo_bridge_open_settings() -> bool {
    // 模拟成功打开设置页面
    true
}

/// 模拟的资源数量获取函数
#[no_mangle]
pub extern "C" fn photo_bridge_get_asset_count(_media_type: i32) -> i32 {
    // 模拟返回固定数量的资源
    42
}

/// 模拟的缩略图数据获取函数
#[no_mangle]
pub extern "C" fn photo_bridge_get_thumbnail_data(
    _asset_id: *const std::os::raw::c_char,
    _width: u32,
    _height: u32,
    callback: extern "C" fn(*const u8, i32),
) {
    // 模拟返回空数据
    callback(std::ptr::null(), 0);
}

/// 模拟的权限引导信息获取函数
#[no_mangle]
pub extern "C" fn photo_bridge_get_current_permission_guidance(
    title_buffer: *mut std::os::raw::c_char,
    title_buffer_size: i32,
    message_buffer: *mut std::os::raw::c_char,
    message_buffer_size: i32,
    action_title_buffer: *mut std::os::raw::c_char,
    action_title_buffer_size: i32,
    steps_buffer: *mut std::os::raw::c_char,
    steps_buffer_size: i32,
) -> bool {
    use std::ffi::CString;

    // 模拟权限引导信息
    let title = CString::new("需要相册访问权限").unwrap();
    let message = CString::new("为了让您能够选择和管理照片，我们需要访问您的相册。").unwrap();
    let action_title = CString::new("授权访问").unwrap();
    let steps =
        CString::new("点击\"授权访问\"按钮\n在弹出的权限对话框中选择\"允许访问所有照片\"").unwrap();

    // 安全地复制字符串到缓冲区
    unsafe {
        if !title_buffer.is_null() && title_buffer_size > 0 {
            let title_bytes = title.as_bytes_with_nul();
            let copy_len = std::cmp::min(title_bytes.len(), title_buffer_size as usize - 1);
            std::ptr::copy_nonoverlapping(title_bytes.as_ptr(), title_buffer as *mut u8, copy_len);
            *title_buffer.add(copy_len) = 0; // 确保以null结尾
        }

        if !message_buffer.is_null() && message_buffer_size > 0 {
            let message_bytes = message.as_bytes_with_nul();
            let copy_len = std::cmp::min(message_bytes.len(), message_buffer_size as usize - 1);
            std::ptr::copy_nonoverlapping(
                message_bytes.as_ptr(),
                message_buffer as *mut u8,
                copy_len,
            );
            *message_buffer.add(copy_len) = 0;
        }

        if !action_title_buffer.is_null() && action_title_buffer_size > 0 {
            let action_title_bytes = action_title.as_bytes_with_nul();
            let copy_len = std::cmp::min(
                action_title_bytes.len(),
                action_title_buffer_size as usize - 1,
            );
            std::ptr::copy_nonoverlapping(
                action_title_bytes.as_ptr(),
                action_title_buffer as *mut u8,
                copy_len,
            );
            *action_title_buffer.add(copy_len) = 0;
        }

        if !steps_buffer.is_null() && steps_buffer_size > 0 {
            let steps_bytes = steps.as_bytes_with_nul();
            let copy_len = std::cmp::min(steps_bytes.len(), steps_buffer_size as usize - 1);
            std::ptr::copy_nonoverlapping(steps_bytes.as_ptr(), steps_buffer as *mut u8, copy_len);
            *steps_buffer.add(copy_len) = 0;
        }
    }

    true // 表示可以打开设置
}

/// 模拟的权限状态监听开始函数
#[no_mangle]
pub extern "C" fn photo_bridge_start_permission_monitoring(_callback: extern "C" fn(i32)) {
    // 模拟开始监听，不做任何操作
}

/// 模拟的权限状态监听停止函数
#[no_mangle]
pub extern "C" fn photo_bridge_stop_permission_monitoring() {
    // 模拟停止监听，不做任何操作
}

/// 模拟的带回调的设置页面打开函数
#[no_mangle]
pub extern "C" fn photo_bridge_open_settings_with_callback(_callback: extern "C" fn(bool)) -> bool {
    // 模拟成功打开设置页面
    true
}
