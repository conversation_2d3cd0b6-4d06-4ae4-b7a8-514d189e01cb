{"builtTestProducts": [{"binaryPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.xctest/Contents/MacOS/SystemPhotoAccessPackageTests", "packagePath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift", "productName": "SystemPhotoAccessPackageTests"}], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.SystemPhotoAccess-arm64-apple-macosx15.0-release.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources", "importPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources"}], "isLibrary": true, "moduleName": "SystemPhotoAccess", "moduleOutputPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule", "objects": ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx11.0", "-O", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/SystemPhotoAccess-Swift.h", "-color-diagnostics", "-swift-version", "5", "-Xcc", "-I/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/include", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g"], "outputFileMapPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift"], "tempsPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build", "wholeModuleOptimization": true}, "C.SystemPhotoAccessPackageTests-arm64-apple-macosx15.0-release.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources", "importPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources"}], "isLibrary": true, "moduleName": "SystemPhotoAccessPackageTests", "moduleOutputPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessPackageTests.swiftmodule", "objects": ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx11.0", "-O", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/SystemPhotoAccessPackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g"], "outputFileMapPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessPackageTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build", "wholeModuleOptimization": true}, "C.SystemPhotoAccessTests-arm64-apple-macosx15.0-release.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources", "importPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/PhotoBridgeTests.swift"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccess.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources"}], "isLibrary": true, "moduleName": "SystemPhotoAccessTests", "moduleOutputPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule", "objects": ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx14.0", "-index-store-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/index/store", "-O", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g"], "outputFileMapPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules/SystemPhotoAccessTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/PhotoBridgeTests.swift"], "tempsPath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build", "wholeModuleOptimization": true}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"SystemPhotoAccess": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "SystemPhotoAccess", "-whole-module-optimization", "-num-threads", "8", "-c", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift", "-I", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules", "-target", "arm64-apple-macosx11.0", "-O", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/SystemPhotoAccess-Swift.h", "-color-diagnostics", "-swift-version", "5", "-Xcc", "-I/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/include", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "SystemPhotoAccessPackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "SystemPhotoAccessPackageTests", "-whole-module-optimization", "-num-threads", "8", "-c", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift", "-I", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules", "-target", "arm64-apple-macosx11.0", "-O", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/SystemPhotoAccessPackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "SystemPhotoAccessTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "SystemPhotoAccessTests", "-whole-module-optimization", "-num-threads", "8", "-c", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/PhotoBridgeTests.swift", "-I", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/Modules", "-target", "arm64-apple-macosx14.0", "-index-store-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/index/store", "-O", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"SystemPhotoAccess": [], "SystemPhotoAccessPackageTests": ["SystemPhotoAccessTests", "SystemPhotoAccess"], "SystemPhotoAccessTests": ["SystemPhotoAccess"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {"/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift": {"inputs": [], "outputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift"}]}}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/sources"}, "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/sources"}, "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/PhotoBridgeTests.swift.o"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessPackageTests.product/Objects.LinkFileList"}, "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Tests/SystemPhotoAccessTests/PhotoBridgeTests.swift"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/SystemPhotoAccessTests.build/sources"}, "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"}}}