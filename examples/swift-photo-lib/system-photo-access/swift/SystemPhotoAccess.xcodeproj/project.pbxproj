// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A2B3C4D5E6F7890 /* PhotoBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7891 /* PhotoBridge.swift */; };
		1A2B3C4D5E6F7892 /* PhotoBridgeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7893 /* PhotoBridgeTests.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A2B3C4D5E6F7890 /* SystemPhotoAccess.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SystemPhotoAccess.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1A2B3C4D5E6F7891 /* PhotoBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoBridge.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7893 /* PhotoBridgeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoBridgeTests.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7894 /* SystemPhotoAccessTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SystemPhotoAccessTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1A2B3C4D5E6F7895 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A2B3C4D5E6F7896 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1A2B3C4D5E6F7897 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A2B3C4D5E6F7898 = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7899 /* Sources */,
				1A2B3C4D5E6F789A /* Tests */,
				1A2B3C4D5E6F789B /* Products */,
			);
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7899 /* Sources */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F789C /* SystemPhotoAccess */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F789A /* Tests */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F789D /* SystemPhotoAccessTests */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F789B /* Products */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890 /* SystemPhotoAccess.framework */,
				1A2B3C4D5E6F7894 /* SystemPhotoAccessTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F789C /* SystemPhotoAccess */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7891 /* PhotoBridge.swift */,
				1A2B3C4D5E6F7895 /* Info.plist */,
			);
			path = SystemPhotoAccess;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F789D /* SystemPhotoAccessTests */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7893 /* PhotoBridgeTests.swift */,
			);
			path = SystemPhotoAccessTests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A2B3C4D5E6F789E /* SystemPhotoAccess */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A2B3C4D5E6F789F /* Build configuration list for PBXNativeTarget "SystemPhotoAccess" */;
			buildPhases = (
				1A2B3C4D5E6F78A0 /* Sources */,
				1A2B3C4D5E6F7896 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SystemPhotoAccess;
			productName = SystemPhotoAccess;
			productReference = 1A2B3C4D5E6F7890 /* SystemPhotoAccess.framework */;
			productType = "com.apple.product-type.framework";
		};
		1A2B3C4D5E6F78A1 /* SystemPhotoAccessTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A2B3C4D5E6F78A2 /* Build configuration list for PBXNativeTarget "SystemPhotoAccessTests" */;
			buildPhases = (
				1A2B3C4D5E6F78A3 /* Sources */,
				1A2B3C4D5E6F7897 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SystemPhotoAccessTests;
			productName = SystemPhotoAccessTests;
			productReference = 1A2B3C4D5E6F7894 /* SystemPhotoAccessTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A2B3C4D5E6F78A4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A2B3C4D5E6F789E = {
						CreatedOnToolsVersion = 15.0;
					};
					1A2B3C4D5E6F78A1 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A2B3C4D5E6F78A5 /* Build configuration list for PBXProject "SystemPhotoAccess" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A2B3C4D5E6F7898;
			productRefGroup = 1A2B3C4D5E6F789B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A2B3C4D5E6F789E /* SystemPhotoAccess */,
				1A2B3C4D5E6F78A1 /* SystemPhotoAccessTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		1A2B3C4D5E6F78A0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D5E6F7890 /* PhotoBridge.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1A2B3C4D5E6F78A3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D5E6F7892 /* PhotoBridgeTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A2B3C4D5E6F78A6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		1A2B3C4D5E6F78A7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A2B3C4D5E6F789F /* Build configuration list for PBXNativeTarget "SystemPhotoAccess" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F78A6 /* Debug */,
				1A2B3C4D5E6F78A7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A2B3C4D5E6F78A2 /* Build configuration list for PBXNativeTarget "SystemPhotoAccessTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F78A6 /* Debug */,
				1A2B3C4D5E6F78A7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A2B3C4D5E6F78A5 /* Build configuration list for PBXProject "SystemPhotoAccess" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F78A6 /* Debug */,
				1A2B3C4D5E6F78A7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A2B3C4D5E6F78A4 /* Project object */;
}
