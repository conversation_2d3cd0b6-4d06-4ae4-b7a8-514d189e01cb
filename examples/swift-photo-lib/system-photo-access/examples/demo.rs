//! 系统相册访问演示程序
//!
//! 这是一个简单的演示程序，展示 swift-bridge 的基本功能。

use system_photo_access::{
    call_swift_hello, check_permission_status_simple, debug_get_count_simple,
    get_asset_count_simple, request_permission_simple, test_function_simple,
};

fn main() {
    println!("🎯 Swift-Bridge 演示程序");
    println!("========================");

    // 1. 调用 Swift 函数
    println!("\n1. 调用 Swift 函数:");
    call_swift_hello();

    // 1.5. 测试函数
    println!("\n1.5. 测试桥接功能:");
    let test_result = test_function_simple();
    println!("测试函数返回值: {} (期望: 12345)", test_result);
    if test_result == 12345 {
        println!("✅ 桥接功能正常");
    } else {
        println!("❌ 桥接功能异常");
    }

    // 2. 检查权限状态
    println!("\n2. 检查权限状态:");
    let mut status = check_permission_status_simple();
    println!("权限状态代码: {}", status);
    match status {
        0 => println!("状态: 未确定"),
        1 => println!("状态: 受限"),
        2 => println!("状态: 拒绝"),
        3 => println!("状态: 已授权"),
        4 => println!("状态: 有限访问"),
        _ => println!("状态: 未知"),
    }

    // 3. 请求权限（如果需要）
    if status == 0 {
        println!("\n3. 请求相册访问权限:");
        println!("正在请求权限，请在弹出的对话框中选择允许...");
        println!("注意：如果没有看到对话框，可能需要手动在系统设置中授权");

        status = request_permission_simple();
        println!("权限请求结果: {}", status);
        match status {
            0 => println!("状态: 未确定"),
            1 => println!("状态: 受限"),
            2 => {
                println!("状态: 拒绝");
                println!("💡 手动授权步骤:");
                println!("   1. 打开 系统设置 > 安全性与隐私 > 隐私");
                println!("   2. 选择左侧的 '照片'");
                println!("   3. 勾选此应用程序的复选框");
            }
            3 => println!("状态: 已授权 ✅"),
            4 => println!("状态: 有限访问 ⚠️"),
            _ => println!("状态: 未知"),
        }
    }

    // 4. 获取资源数量
    println!("\n4. 获取资源数量:");
    if status == 3 || status == 4 {
        let count = get_asset_count_simple(0); // 0 表示所有类型
        println!("总资源数量: {}", count);

        let image_count = get_asset_count_simple(1); // 1 表示图片
        println!("图片数量: {}", image_count);

        let video_count = get_asset_count_simple(2); // 2 表示视频
        println!("视频数量: {}", video_count);
    } else {
        println!("❌ 没有相册访问权限，无法获取资源数量");
        println!("💡 请重新运行程序并在权限对话框中选择'允许'");

        // 5. 调试测试：尝试强制获取
        println!("\n5. 调试测试（强制获取）:");
        println!("尝试直接调用 PHAsset.fetchAssets...");
        let debug_count = debug_get_count_simple();
        println!("调试获取结果: {} 张照片", debug_count);

        if debug_count > 0 {
            println!("🤔 奇怪！调试函数能获取到照片，但正常函数不行");
            println!("这可能表明权限检查逻辑有问题");
        } else {
            println!("📝 调试函数也返回 0，确认是权限问题");
        }
    }

    println!("\n✅ 演示完成!");
}
