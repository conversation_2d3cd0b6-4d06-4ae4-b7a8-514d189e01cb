//! 基础使用示例
//! 
//! 演示如何使用系统相册访问库的基本功能。

use system_photo_access::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    system_photo_access::init();
    
    println!("=== 系统相册访问库基础使用示例 ===");
    println!("库版本: {}", system_photo_access::VERSION);
    
    // 1. 检查权限状态
    println!("\n1. 检查权限状态");
    match check_permission_status() {
        Ok(status) => {
            println!("当前权限状态: {:?}", status);
            println!("状态描述: {}", status.description());
            println!("是否有访问权限: {}", status.has_access());
        }
        Err(error) => {
            println!("权限检查失败: {}", error);
            return Err(error.into());
        }
    }
    
    // 2. 确保拥有权限
    println!("\n2. 确保拥有相册访问权限");
    match ensure_photo_permission().await {
        Ok(status) => {
            println!("权限确认成功: {:?}", status);
            
            if !status.has_access() {
                println!("无法获得相册访问权限，程序退出");
                return Ok(());
            }
        }
        Err(PhotoError::PermissionDenied) => {
            println!("权限被拒绝，尝试打开设置页面");
            if let Err(e) = open_settings() {
                println!("无法打开设置页面: {}", e);
            }
            return Ok(());
        }
        Err(PhotoError::PermissionRestricted) => {
            println!("权限受限，可能由于家长控制等原因");
            return Ok(());
        }
        Err(error) => {
            println!("权限检查失败: {}", error);
            return Err(error.into());
        }
    }
    
    // 3. 获取相册资源数量
    println!("\n3. 获取相册资源数量");
    match get_asset_count(None).await {
        Ok(total_count) => {
            println!("总资源数量: {}", total_count);
        }
        Err(error) => {
            println!("获取资源数量失败: {}", error);
        }
    }
    
    // 获取图片数量
    match get_asset_count(Some(MediaType::Image)).await {
        Ok(image_count) => {
            println!("图片数量: {}", image_count);
        }
        Err(error) => {
            println!("获取图片数量失败: {}", error);
        }
    }
    
    // 获取视频数量
    match get_asset_count(Some(MediaType::Video)).await {
        Ok(video_count) => {
            println!("视频数量: {}", video_count);
        }
        Err(error) => {
            println!("获取视频数量失败: {}", error);
        }
    }
    
    // 4. 获取相册列表
    println!("\n4. 获取相册列表");
    let query = PhotoQuery::images_only()
        .with_pagination(0, 10)
        .with_sort_order(SortOrder::CreationDateDescending);
    
    match get_photo_assets(query).await {
        Ok(assets) => {
            println!("获取到 {} 个图片资源", assets.len());
            
            for (index, asset) in assets.iter().enumerate() {
                println!("  {}. ID: {}", index + 1, asset.id);
                println!("     类型: {:?}", asset.media_type);
                println!("     尺寸: {}x{}", asset.pixel_width, asset.pixel_height);
                
                if let Some(creation_date) = &asset.creation_date {
                    println!("     创建时间: {}", creation_date.format("%Y-%m-%d %H:%M:%S"));
                }
                
                if let Some(file_size) = asset.file_size {
                    println!("     文件大小: {} KB", file_size / 1024);
                }
                
                println!("     是否收藏: {}", asset.is_favorite);
                println!("     是否在云端: {}", asset.is_in_cloud);
                println!();
            }
        }
        Err(error) => {
            println!("获取相册列表失败: {}", error);
        }
    }
    
    // 5. 获取缩略图示例
    println!("\n5. 获取缩略图示例");
    let test_asset_id = "test-asset-id";
    let thumbnail_size = ImageSize::new(200, 200);
    
    match get_thumbnail(test_asset_id, thumbnail_size).await {
        Ok(image_data) => {
            println!("成功获取缩略图:");
            println!("  格式: {:?}", image_data.format);
            println!("  尺寸: {}x{}", image_data.size.width, image_data.size.height);
            println!("  数据大小: {} 字节", image_data.byte_size);
            println!("  MIME 类型: {}", image_data.mime_type());
        }
        Err(error) => {
            println!("获取缩略图失败: {}", error);
            println!("错误描述: {}", error.user_description());
            
            if let Some(action) = error.suggested_action() {
                println!("建议操作: {}", action);
            }
        }
    }
    
    println!("\n=== 示例完成 ===");
    Ok(())
}
