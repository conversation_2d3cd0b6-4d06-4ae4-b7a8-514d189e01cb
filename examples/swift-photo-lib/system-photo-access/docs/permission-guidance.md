# 权限引导功能文档

## 概述

权限引导功能是 TASK-006 的核心实现，旨在为用户提供友好的权限管理体验。当相册访问权限被拒绝或受限时，系统会自动提供详细的引导信息，帮助用户正确设置权限。

## 功能特性

### 1. 智能权限引导

- **状态感知**: 根据当前权限状态提供相应的引导信息
- **多语言支持**: 支持中文、英文等多种语言
- **详细步骤**: 提供具体的操作步骤指导
- **平台适配**: 针对 iOS 和 macOS 提供不同的引导内容

### 2. 权限状态监听

- **实时监听**: 监听权限状态变化
- **自动回调**: 权限状态改变时自动通知应用
- **应用状态感知**: 检测应用从后台返回前台时的权限变化

### 3. 用户友好的错误处理

- **带引导的错误**: 权限错误自动包含引导信息
- **操作建议**: 提供具体的解决方案
- **设置页面快捷打开**: 一键跳转到系统设置

## API 参考

### 权限引导结构体

```rust
pub struct PermissionGuidance {
    /// 引导标题
    pub title: String,
    /// 引导消息
    pub message: String,
    /// 操作按钮标题
    pub action_title: String,
    /// 操作步骤列表
    pub steps: Vec<String>,
    /// 是否可以打开设置页面
    pub can_open_settings: bool,
}
```

### 核心函数

#### 获取权限引导信息

```rust
// 获取当前权限状态的引导信息
pub fn get_permission_guidance() -> PhotoResult<PermissionGuidance>

// 获取指定权限状态的引导信息
pub fn get_permission_guidance_for_status(status: PhotoPermissionStatus) -> PermissionGuidance
```

#### 权限状态监听

```rust
// 开始权限状态监听
pub fn start_permission_monitoring<F>(callback: F)
where F: Fn(PhotoPermissionStatus) + Send + Sync + 'static

// 停止权限状态监听
pub fn stop_permission_monitoring()
```

#### 带引导的权限管理

```rust
// 带引导的权限请求
pub async fn request_permission_with_guidance() -> PermissionResult

// 带引导的权限确保
pub async fn ensure_photo_permission_with_guidance() -> PermissionResult
```

#### 设置页面操作

```rust
// 打开系统设置页面
pub fn open_settings() -> PhotoResult<()>

// 异步打开设置页面（带完成回调）
pub async fn open_settings_async() -> PhotoResult<bool>
```

### 错误类型扩展

```rust
// 带引导信息的权限错误
PhotoError::PermissionDeniedWithGuidance {
    guidance_message: String,
    guidance: PermissionGuidance,
    context: Option<ErrorContext>,
}

PhotoError::PermissionRestrictedWithGuidance {
    guidance_message: String,
    guidance: PermissionGuidance,
    context: Option<ErrorContext>,
}
```

## 使用示例

### 基本权限检查和引导

```rust
use system_photo_access::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 检查当前权限状态
    let status = check_permission_status()?;
    
    if !status.has_access() {
        // 获取引导信息
        let guidance = get_permission_guidance_for_status(status);
        
        println!("权限引导:");
        println!("标题: {}", guidance.title);
        println!("消息: {}", guidance.message);
        
        for (i, step) in guidance.steps.iter().enumerate() {
            println!("{}. {}", i + 1, step);
        }
        
        if guidance.can_open_settings {
            println!("正在打开设置页面...");
            open_settings()?;
        }
    }
    
    Ok(())
}
```

### 带引导的权限确保

```rust
use system_photo_access::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    match ensure_photo_permission_with_guidance().await {
        Ok(status) if status.has_access() => {
            println!("✅ 已获得相册访问权限");
            // 继续相册操作
        }
        Err(error) => {
            println!("❌ 权限获取失败: {}", error);
            
            // 如果错误包含引导信息，显示给用户
            if let Some(guidance) = error.get_permission_guidance() {
                println!("\n📋 解决方案:");
                println!("{}", guidance.message);
                
                if guidance.can_open_settings {
                    // 询问用户是否要打开设置
                    println!("是否要打开设置页面？");
                    // ... 用户交互逻辑
                }
            }
        }
    }
    
    Ok(())
}
```

### 权限状态监听

```rust
use system_photo_access::*;

fn setup_permission_monitoring() {
    start_permission_monitoring(Box::new(|status| {
        println!("权限状态变化: {:?}", status);
        
        match status {
            PhotoPermissionStatus::Authorized => {
                println!("🎉 获得完全访问权限！");
                // 通知应用可以继续相册操作
            }
            PhotoPermissionStatus::Limited => {
                println!("📝 获得有限访问权限");
                // 通知应用可以访问选定的照片
            }
            PhotoPermissionStatus::Denied => {
                println!("❌ 权限被拒绝");
                // 显示引导信息
            }
            _ => {}
        }
    }));
}

fn cleanup_permission_monitoring() {
    stop_permission_monitoring();
}
```

## 平台差异

### iOS 平台

- 支持 iOS 14+ 的有限权限模式
- 使用 `UIApplication.openSettingsURLString` 打开设置
- 支持权限对话框的自动显示

### macOS 平台

- 使用 `NSWorkspace` 打开系统偏好设置
- 直接跳转到安全与隐私面板的照片权限设置
- 支持新旧版本的系统偏好设置

## 最佳实践

### 1. 权限检查时机

- 应用启动时检查权限状态
- 在需要访问相册前再次确认权限
- 从后台返回前台时检查权限变化

### 2. 用户体验优化

- 在请求权限前先解释为什么需要权限
- 提供清晰的操作步骤指导
- 避免重复显示权限对话框

### 3. 错误处理

- 使用带引导的权限函数获得更好的用户体验
- 检查错误是否包含引导信息
- 为用户提供明确的解决方案

### 4. 权限监听

- 在适当的时机启动权限监听
- 及时停止不需要的监听以节省资源
- 在权限状态变化时更新 UI

## 故障排除

### 常见问题

1. **设置页面无法打开**
   - 检查平台兼容性
   - 确认应用有打开 URL 的权限

2. **权限监听不工作**
   - 确认已正确启动监听
   - 检查应用状态监听是否正常

3. **引导信息显示不正确**
   - 检查权限状态获取是否正确
   - 确认语言设置

### 调试技巧

- 启用详细日志记录
- 使用模拟器测试不同权限状态
- 检查系统设置中的权限配置

## 更新日志

### TASK-006 实现 (当前版本)

- ✅ 实现权限引导信息获取
- ✅ 添加权限状态监听功能
- ✅ 增强设置页面打开功能
- ✅ 创建带引导的权限管理 API
- ✅ 扩展错误类型支持引导信息
- ✅ 添加完整的测试用例
- ✅ 提供使用示例和文档
