[workspace]

[package]
name = "system-photo-access"
version = "0.1.0"
edition = "2021"
authors = ["swift-bridge contributors"]
description = "System photo library access for Rust applications using swift-bridge"
license = "MIT OR Apache-2.0"
repository = "https://github.com/chinedufn/swift-bridge"
keywords = ["swift", "ios", "macos", "photos", "bridge"]
categories = ["api-bindings", "multimedia::images"]


[lib]
name = "system_photo_access"
crate-type = ["rlib", "staticlib", "cdylib"]

[[example]]
name = "demo"
path = "examples/demo.rs"


[dependencies]
# swift-bridge 核心依赖
swift-bridge = { path = "../../../.." }

# 异步运行时
tokio = { version = "1.0", features = [
    "rt-multi-thread",
    "macros",
    "sync",
    "time",
] }

# 错误处理
thiserror = "1.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID 生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 序列化支持
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 日志记录
log = "0.4"
tracing = "0.1"
tracing-subscriber = "0.3"

# 静态变量支持
lazy_static = "1.4"

[dev-dependencies]
# 测试框架
tokio-test = "0.4"
tempfile = "3.0"

# 性能基准测试
criterion = "0.5"

# Mock 测试
mockall = "0.11"

[build-dependencies]
swift-bridge-build = { path = "../../../../crates/swift-bridge-build" }

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"


[features]
default = []
# 开发工具功能
dev-utils = []
# 性能分析功能
profiling = ["tracing/max_level_trace"]
