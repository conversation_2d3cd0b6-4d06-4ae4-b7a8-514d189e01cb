{"": {"dependencies": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/SystemPhotoAccess.d", "object": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/SystemPhotoAccess.o", "swift-dependencies": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/master.swiftdeps"}, "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/swift/Sources/SystemPhotoAccess/PhotoBridge.swift": {"object": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swift.o", "swiftmodule": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge/examples/system-photo-access/.build/arm64-apple-macosx/release/SystemPhotoAccess.build/PhotoBridge.swiftdeps"}}