<?xml version="1.0"?><coverage lines-covered="0" lines-valid="392" line-rate="0" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0" version="1.9" timestamp="1754303876"><sources><source>/Users/<USER>/local_doc/l_dev/my/rust/swift-bridge</source></sources><packages><package name="examples/system-photo-access/tests" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/codegen" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="SwiftRustIntegrationTestRunner/integration-test-create-swift-package/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/rust-binary-calls-swift-package" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/parse/parse_enum" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/parse/parse_extern_mod/generics" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-build/src" line-rate="0" branch-rate="0" complexity="0"><classes><class name="lib" filename="crates/swift-bridge-build/src/lib.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="21" hits="0"/><line number="23" hits="0"/><line number="24" hits="0"/><line number="26" hits="0"/><line number="27" hits="0"/><line number="28" hits="0"/><line number="29" hits="0"/><line number="31" hits="0"/><line number="36" hits="0"/><line number="41" hits="0"/><line number="44" hits="0"/><line number="61" hits="0"/><line number="62" hits="0"/><line number="64" hits="0"/><line number="65" hits="0"/><line number="67" hits="0"/><line number="68" hits="0"/><line number="69" hits="0"/><line number="72" hits="0"/><line number="73" hits="0"/><line number="74" hits="0"/><line number="75" hits="0"/><line number="78" hits="0"/><line number="80" hits="0"/><line number="81" hits="0"/><line number="85" hits="0"/></lines></class></classes></package><package name="crates/swift-integration-tests" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="SwiftRustIntegrationTestRunner/swift-package-rust-library-fixture" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/codegen/generate_swift" line-rate="0" branch-rate="0" complexity="0"><classes><class name="shared_struct" filename="crates/swift-bridge-ir/src/codegen/generate_swift/shared_struct.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="111" hits="0"/><line number="113" hits="0"/><line number="114" hits="0"/><line number="116" hits="0"/><line number="117" hits="0"/><line number="118" hits="0"/><line number="119" hits="0"/><line number="120" hits="0"/><line number="121" hits="0"/><line number="122" hits="0"/><line number="127" hits="0"/><line number="128" hits="0"/><line number="131" hits="0"/><line number="141" hits="0"/><line number="143" hits="0"/><line number="144" hits="0"/><line number="145" hits="0"/><line number="146" hits="0"/><line number="147" hits="0"/><line number="151" hits="0"/><line number="152" hits="0"/><line number="155" hits="0"/><line number="162" hits="0"/><line number="164" hits="0"/><line number="165" hits="0"/><line number="167" hits="0"/><line number="168" hits="0"/><line number="169" hits="0"/><line number="170" hits="0"/><line number="171" hits="0"/><line number="172" hits="0"/><line number="173" hits="0"/><line number="178" hits="0"/><line number="179" hits="0"/><line number="182" hits="0"/></lines></class></classes></package><package name="crates/swift-bridge-ir/src/parse/parse_extern_mod" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-macro/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/bridged_type/shared_enum" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/parsed_extern_fn" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src/opaque_type_attributes" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/errors" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/async-functions" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/system-photo-access" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="SwiftRustIntegrationTestRunner/swift-package-rust-library-fixture/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/system-photo-access/examples" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src/argument_attributes" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src/shared_types" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/codegen-visualizer" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/codegen/generate_rust_tokens" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-macro/tests/ui" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/codegen-visualizer/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/multiple-bridge-modules/src/bridge" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src" line-rate="0" branch-rate="0" complexity="0"><classes><class name="option" filename="crates/swift-integration-tests/src/option.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="255" hits="0"/><line number="264" hits="0"/></lines></class></classes></package><package name="crates/swift-bridge-build/src/generate_core" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src" line-rate="0" branch-rate="0" complexity="0"><classes><class name="bridged_type" filename="crates/swift-bridge-ir/src/bridged_type.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="65" hits="0"/><line number="66" hits="0"/><line number="73" hits="0"/><line number="74" hits="0"/><line number="308" hits="0"/><line number="309" hits="0"/><line number="313" hits="0"/></lines></class></classes></package><package name="crates/swift-bridge-ir/src/bridged_type/shared_struct" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/codegen/codegen_tests" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/codegen/generate_rust_tokens/vec" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/parse" line-rate="0" branch-rate="0" complexity="0"><classes><class name="parse_enum" filename="crates/swift-bridge-ir/src/parse/parse_enum.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="15" hits="0"/><line number="16" hits="0"/><line number="18" hits="0"/><line number="19" hits="0"/><line number="21" hits="0"/><line number="23" hits="0"/><line number="25" hits="0"/><line number="26" hits="0"/><line number="28" hits="0"/><line number="32" hits="0"/><line number="34" hits="0"/><line number="35" hits="0"/><line number="36" hits="0"/><line number="39" hits="0"/></lines></class><class name="parse_extern_mod" filename="crates/swift-bridge-ir/src/parse/parse_extern_mod.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="39" hits="0"/><line number="40" hits="0"/><line number="41" hits="0"/><line number="42" hits="0"/><line number="44" hits="0"/><line number="47" hits="0"/><line number="49" hits="0"/><line number="50" hits="0"/><line number="51" hits="0"/><line number="52" hits="0"/><line number="53" hits="0"/><line number="54" hits="0"/><line number="58" hits="0"/><line number="59" hits="0"/><line number="60" hits="0"/><line number="62" hits="0"/><line number="66" hits="0"/><line number="67" hits="0"/><line number="68" hits="0"/><line number="69" hits="0"/><line number="72" hits="0"/><line number="75" hits="0"/><line number="76" hits="0"/><line number="78" hits="0"/><line number="79" hits="0"/><line number="80" hits="0"/><line number="86" hits="0"/><line number="88" hits="0"/><line number="89" hits="0"/><line number="91" hits="0"/><line number="92" hits="0"/><line number="93" hits="0"/><line number="95" hits="0"/><line number="97" hits="0"/><line number="98" hits="0"/><line number="100" hits="0"/><line number="101" hits="0"/><line number="104" hits="0"/><line number="105" hits="0"/><line number="106" hits="0"/><line number="107" hits="0"/><line number="108" hits="0"/><line number="113" hits="0"/><line number="114" hits="0"/><line number="115" hits="0"/><line number="116" hits="0"/><line number="117" hits="0"/><line number="119" hits="0"/><line number="120" hits="0"/><line number="121" hits="0"/><line number="122" hits="0"/><line number="123" hits="0"/><line number="124" hits="0"/><line number="127" hits="0"/><line number="128" hits="0"/><line number="131" hits="0"/><line number="133" hits="0"/><line number="134" hits="0"/><line number="135" hits="0"/><line number="136" hits="0"/><line number="137" hits="0"/><line number="138" hits="0"/><line number="141" hits="0"/><line number="142" hits="0"/><line number="144" hits="0"/><line number="145" hits="0"/><line number="146" hits="0"/><line number="147" hits="0"/><line number="148" hits="0"/><line number="150" hits="0"/><line number="151" hits="0"/><line number="152" hits="0"/><line number="157" hits="0"/><line number="159" hits="0"/><line number="160" hits="0"/><line number="161" hits="0"/><line number="162" hits="0"/><line number="163" hits="0"/><line number="168" hits="0"/><line number="169" hits="0"/><line number="170" hits="0"/><line number="171" hits="0"/><line number="172" hits="0"/><line number="178" hits="0"/><line number="179" hits="0"/><line number="180" hits="0"/><line number="182" hits="0"/><line number="183" hits="0"/><line number="184" hits="0"/><line number="185" hits="0"/><line number="186" hits="0"/><line number="188" hits="0"/><line number="190" hits="0"/><line number="193" hits="0"/><line number="194" hits="0"/><line number="195" hits="0"/><line number="197" hits="0"/><line number="198" hits="0"/><line number="199" hits="0"/><line number="200" hits="0"/><line number="201" hits="0"/><line number="202" hits="0"/><line number="203" hits="0"/><line number="204" hits="0"/><line number="205" hits="0"/><line number="207" hits="0"/><line number="212" hits="0"/><line number="215" hits="0"/><line number="216" hits="0"/><line number="217" hits="0"/><line number="218" hits="0"/><line number="219" hits="0"/><line number="220" hits="0"/><line number="221" hits="0"/><line number="222" hits="0"/><line number="227" hits="0"/><line number="228" hits="0"/><line number="230" hits="0"/><line number="231" hits="0"/><line number="232" hits="0"/><line number="233" hits="0"/><line number="242" hits="0"/><line number="244" hits="0"/><line number="246" hits="0"/><line number="247" hits="0"/><line number="248" hits="0"/><line number="249" hits="0"/><line number="250" hits="0"/><line number="251" hits="0"/><line number="254" hits="0"/><line number="256" hits="0"/><line number="257" hits="0"/><line number="258" hits="0"/><line number="260" hits="0"/><line number="263" hits="0"/><line number="265" hits="0"/><line number="268" hits="0"/><line number="283" hits="0"/><line number="284" hits="0"/><line number="285" hits="0"/><line number="287" hits="0"/><line number="289" hits="0"/><line number="290" hits="0"/><line number="291" hits="0"/><line number="292" hits="0"/><line number="293" hits="0"/><line number="296" hits="0"/><line number="300" hits="0"/><line number="311" hits="0"/><line number="312" hits="0"/><line number="313" hits="0"/><line number="314" hits="0"/><line number="315" hits="0"/><line number="318" hits="0"/><line number="319" hits="0"/><line number="320" hits="0"/><line number="321" hits="0"/><line number="323" hits="0"/><line number="324" hits="0"/><line number="326" hits="0"/><line number="329" hits="0"/><line number="330" hits="0"/><line number="331" hits="0"/><line number="332" hits="0"/><line number="333" hits="0"/><line number="334" hits="0"/><line number="335" hits="0"/><line number="340" hits="0"/><line number="342" hits="0"/><line number="344" hits="0"/><line number="345" hits="0"/><line number="346" hits="0"/><line number="348" hits="0"/><line number="349" hits="0"/><line number="350" hits="0"/><line number="351" hits="0"/><line number="352" hits="0"/><line number="353" hits="0"/><line number="355" hits="0"/><line number="358" hits="0"/><line number="429" hits="0"/></lines></class><class name="parse_struct" filename="crates/swift-bridge-ir/src/parse/parse_struct.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="91" hits="0"/><line number="92" hits="0"/><line number="94" hits="0"/><line number="96" hits="0"/><line number="97" hits="0"/><line number="99" hits="0"/><line number="100" hits="0"/><line number="101" hits="0"/><line number="103" hits="0"/><line number="104" hits="0"/><line number="105" hits="0"/><line number="106" hits="0"/><line number="108" hits="0"/><line number="109" hits="0"/><line number="111" hits="0"/><line number="112" hits="0"/><line number="113" hits="0"/><line number="114" hits="0"/><line number="116" hits="0"/><line number="118" hits="0"/><line number="119" hits="0"/><line number="120" hits="0"/><line number="124" hits="0"/><line number="125" hits="0"/><line number="130" hits="0"/><line number="131" hits="0"/><line number="132" hits="0"/><line number="133" hits="0"/><line number="134" hits="0"/><line number="135" hits="0"/><line number="136" hits="0"/><line number="142" hits="0"/><line number="152" hits="0"/><line number="153" hits="0"/><line number="154" hits="0"/><line number="155" hits="0"/><line number="156" hits="0"/><line number="157" hits="0"/><line number="162" hits="0"/><line number="163" hits="0"/><line number="164" hits="0"/><line number="166" hits="0"/><line number="167" hits="0"/><line number="170" hits="0"/><line number="174" hits="0"/><line number="176" hits="0"/><line number="177" hits="0"/><line number="178" hits="0"/><line number="179" hits="0"/><line number="182" hits="0"/></lines></class><class name="type_declarations" filename="crates/swift-bridge-ir/src/parse/type_declarations.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="220" hits="0"/></lines></class></classes></package><package name="crates/swift-bridge-ir/src/parse/type_declarations" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src/enum_attributes" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-cli/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/bridged_type" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src/function_attributes" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/async-functions/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/multiple-bridge-modules" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-bridge-ir/src/bridged_type/shared_struct/struct_field" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/without-a-bridge-module/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="src" line-rate="0" branch-rate="0" complexity="0"><classes><class name="boxed_fn_support" filename="src/boxed_fn_support.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="4" hits="0"/><line number="7" hits="0"/><line number="11" hits="0"/><line number="15" hits="0"/></lines></class><class name="lib" filename="src/lib.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="39" hits="0"/><line number="41" hits="0"/><line number="42" hits="0"/><line number="47" hits="0"/><line number="48" hits="0"/><line number="102" hits="0"/><line number="103" hits="0"/></lines></class></classes></package><package name="examples/rust-binary-calls-swift-package/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/system-photo-access/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="examples/multiple-bridge-modules/src" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="crates/swift-integration-tests/src/struct_attributes" line-rate="0" branch-rate="0" complexity="0"><classes></classes></package><package name="src/std_bridge" line-rate="0" branch-rate="0" complexity="0"><classes><class name="rust_vec" filename="src/std_bridge/rust_vec.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="26" hits="0"/><line number="27" hits="0"/><line number="32" hits="0"/><line number="33" hits="0"/><line number="34" hits="0"/><line number="39" hits="0"/><line number="40" hits="0"/><line number="41" hits="0"/><line number="46" hits="0"/><line number="47" hits="0"/><line number="48" hits="0"/><line number="53" hits="0"/><line number="54" hits="0"/><line number="55" hits="0"/><line number="58" hits="0"/><line number="59" hits="0"/><line number="60" hits="0"/><line number="69" hits="0"/><line number="70" hits="0"/><line number="71" hits="0"/><line number="72" hits="0"/><line number="73" hits="0"/><line number="74" hits="0"/><line number="80" hits="0"/><line number="81" hits="0"/><line number="82" hits="0"/><line number="91" hits="0"/><line number="92" hits="0"/><line number="93" hits="0"/><line number="94" hits="0"/><line number="95" hits="0"/><line number="96" hits="0"/><line number="102" hits="0"/><line number="103" hits="0"/><line number="104" hits="0"/><line number="111" hits="0"/><line number="112" hits="0"/><line number="113" hits="0"/></lines></class><class name="string" filename="src/std_bridge/string.rs" line-rate="0" branch-rate="0" complexity="0"><methods/><lines><line number="84" hits="0"/><line number="85" hits="0"/><line number="88" hits="0"/><line number="89" hits="0"/><line number="92" hits="0"/><line number="93" hits="0"/><line number="96" hits="0"/><line number="97" hits="0"/><line number="100" hits="0"/><line number="101" hits="0"/><line number="107" hits="0"/><line number="108" hits="0"/><line number="113" hits="0"/><line number="114" hits="0"/><line number="118" hits="0"/><line number="119" hits="0"/><line number="120" hits="0"/><line number="123" hits="0"/><line number="124" hits="0"/><line number="127" hits="0"/><line number="129" hits="0"/><line number="130" hits="0"/><line number="136" hits="0"/><line number="138" hits="0"/><line number="139" hits="0"/><line number="146" hits="0"/><line number="147" hits="0"/></lines></class></classes></package></packages></coverage>