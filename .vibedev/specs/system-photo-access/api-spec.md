# API 规格说明 - 系统相册访问功能

## API 概述

本 API 提供 Rust 应用访问系统相册的完整功能，包括权限管理、相册内容读取、图片数据获取和错误处理。所有 API 都遵循 Rust 的惯用法，使用 `Result<T, E>` 进行错误处理，支持同步和异步操作。

## 核心数据类型

### 权限相关类型

```rust
/// 相册访问权限状态
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum PhotoPermissionStatus {
    /// 尚未请求权限
    NotDetermined,
    /// 权限受限（家长控制等）
    Restricted,
    /// 用户拒绝权限
    Denied,
    /// 用户授权完全访问
    Authorized,
    /// 用户授权有限访问（iOS 14+）
    Limited,
}

/// 权限请求结果
pub type PermissionResult = Result<PhotoPermissionStatus, PhotoError>;
```

### 媒体资源类型

```rust
/// 媒体类型
#[derive(Debu<PERSON>, <PERSON>lone, PartialEq, Eq)]
pub enum MediaType {
    /// 图片
    Image,
    /// 视频
    Video,
    /// 音频
    Audio,
    /// 未知类型
    Unknown,
}

/// 图片格式
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ImageFormat {
    JPEG,
    PNG,
    HEIC,
    GIF,
    TIFF,
    Unknown(String),
}

/// 图片尺寸
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ImageSize {
    pub width: u32,
    pub height: u32,
}

/// 媒体资源信息
#[derive(Debug, Clone)]
pub struct PhotoAsset {
    /// 唯一标识符
    pub id: String,
    /// 媒体类型
    pub media_type: MediaType,
    /// 创建时间
    pub creation_date: Option<chrono::DateTime<chrono::Utc>>,
    /// 修改时间
    pub modification_date: Option<chrono::DateTime<chrono::Utc>>,
    /// 像素宽度
    pub pixel_width: u32,
    /// 像素高度
    pub pixel_height: u32,
    /// 视频时长（秒）
    pub duration: Option<f64>,
    /// 文件大小（字节）
    pub file_size: Option<u64>,
    /// 是否为收藏
    pub is_favorite: bool,
    /// 是否在云端
    pub is_in_cloud: bool,
}

/// 图片数据
#[derive(Debug)]
pub struct ImageData {
    /// 图片二进制数据
    pub data: Vec<u8>,
    /// 图片格式
    pub format: ImageFormat,
    /// 图片尺寸
    pub size: ImageSize,
    /// 数据大小
    pub byte_size: usize,
}
```

### 查询和筛选类型

```rust
/// 媒体查询选项
#[derive(Debug, Clone, Default)]
pub struct PhotoQuery {
    /// 媒体类型筛选
    pub media_type: Option<MediaType>,
    /// 是否只包含收藏
    pub favorites_only: bool,
    /// 创建时间范围
    pub date_range: Option<(chrono::DateTime<chrono::Utc>, chrono::DateTime<chrono::Utc>)>,
    /// 排序方式
    pub sort_order: SortOrder,
    /// 分页偏移
    pub offset: usize,
    /// 每页数量
    pub limit: usize,
}

/// 排序方式
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SortOrder {
    /// 按创建时间升序
    CreationDateAscending,
    /// 按创建时间降序
    CreationDateDescending,
    /// 按修改时间升序
    ModificationDateAscending,
    /// 按修改时间降序
    ModificationDateDescending,
}

/// 图片请求选项
#[derive(Debug, Clone)]
pub struct ImageRequestOptions {
    /// 目标尺寸（None 表示原图）
    pub target_size: Option<ImageSize>,
    /// 内容模式
    pub content_mode: ContentMode,
    /// 图片质量（0.0-1.0）
    pub quality: f32,
    /// 是否允许网络下载
    pub allow_network_access: bool,
}

/// 内容模式
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ContentMode {
    /// 等比缩放适应
    AspectFit,
    /// 等比缩放填充
    AspectFill,
    /// 拉伸填充
    Fill,
}
```

### 错误类型

```rust
/// 相册访问错误
#[derive(Debug, thiserror::Error)]
pub enum PhotoError {
    /// 权限被拒绝
    #[error("相册访问权限被拒绝")]
    PermissionDenied,
    
    /// 权限受限
    #[error("相册访问权限受限")]
    PermissionRestricted,
    
    /// 资源不存在
    #[error("媒体资源不存在: {id}")]
    AssetNotFound { id: String },
    
    /// 网络错误
    #[error("网络访问失败: {message}")]
    NetworkError { message: String },
    
    /// 系统错误
    #[error("系统错误: {message}")]
    SystemError { message: String },
    
    /// 数据格式错误
    #[error("数据格式不支持: {format}")]
    UnsupportedFormat { format: String },
    
    /// 内存不足
    #[error("内存不足")]
    OutOfMemory,
    
    /// 操作被取消
    #[error("操作被用户取消")]
    Cancelled,
    
    /// 超时错误
    #[error("操作超时")]
    Timeout,
}
```

## 权限管理 API

### 权限状态检查

```rust
/// 检查当前相册访问权限状态
/// 
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 当前权限状态
/// - `Err(PhotoError)`: 检查失败的错误信息
pub fn check_permission_status() -> Result<PhotoPermissionStatus, PhotoError>;
```

### 权限请求

```rust
/// 请求相册访问权限（异步）
/// 
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 用户授权后的权限状态
/// - `Err(PhotoError)`: 权限请求失败的错误信息
/// 
/// # 注意
/// - 如果权限已被拒绝，此函数不会再次显示权限对话框
/// - 建议在调用前先检查权限状态
pub async fn request_permission() -> PermissionResult;

/// 请求相册访问权限（回调版本）
/// 
/// # 参数
/// - `callback`: 权限请求完成后的回调函数
pub fn request_permission_with_callback<F>(callback: F) 
where 
    F: FnOnce(PermissionResult) + Send + 'static;
```

## 相册内容访问 API

### 获取媒体列表

```rust
/// 获取相册中的媒体资源列表
/// 
/// # 参数
/// - `query`: 查询选项，包含筛选和分页参数
/// 
/// # 返回值
/// - `Ok(Vec<PhotoAsset>)`: 媒体资源列表
/// - `Err(PhotoError)`: 获取失败的错误信息
/// 
/// # 示例
/// ```rust
/// let query = PhotoQuery {
///     media_type: Some(MediaType::Image),
///     limit: 50,
///     ..Default::default()
/// };
/// let assets = get_photo_assets(query).await?;
/// ```
pub async fn get_photo_assets(query: PhotoQuery) -> Result<Vec<PhotoAsset>, PhotoError>;

/// 获取媒体资源总数
/// 
/// # 参数
/// - `media_type`: 可选的媒体类型筛选
/// 
/// # 返回值
/// - `Ok(usize)`: 媒体资源总数
/// - `Err(PhotoError)`: 获取失败的错误信息
pub async fn get_asset_count(media_type: Option<MediaType>) -> Result<usize, PhotoError>;
```

### 获取单个资源信息

```rust
/// 根据 ID 获取媒体资源信息
/// 
/// # 参数
/// - `asset_id`: 媒体资源的唯一标识符
/// 
/// # 返回值
/// - `Ok(PhotoAsset)`: 媒体资源信息
/// - `Err(PhotoError)`: 获取失败的错误信息
pub async fn get_asset_by_id(asset_id: &str) -> Result<PhotoAsset, PhotoError>;
```

## 图片数据访问 API

### 获取图片数据

```rust
/// 获取图片的二进制数据
/// 
/// # 参数
/// - `asset_id`: 图片资源的唯一标识符
/// - `options`: 图片请求选项
/// 
/// # 返回值
/// - `Ok(ImageData)`: 图片数据
/// - `Err(PhotoError)`: 获取失败的错误信息
/// 
/// # 示例
/// ```rust
/// let options = ImageRequestOptions {
///     target_size: Some(ImageSize { width: 300, height: 300 }),
///     content_mode: ContentMode::AspectFit,
///     quality: 0.8,
///     allow_network_access: true,
/// };
/// let image_data = get_image_data("asset_id", options).await?;
/// ```
pub async fn get_image_data(
    asset_id: &str, 
    options: ImageRequestOptions
) -> Result<ImageData, PhotoError>;

/// 获取图片缩略图
/// 
/// # 参数
/// - `asset_id`: 图片资源的唯一标识符
/// - `size`: 缩略图尺寸
/// 
/// # 返回值
/// - `Ok(ImageData)`: 缩略图数据
/// - `Err(PhotoError)`: 获取失败的错误信息
pub async fn get_thumbnail(
    asset_id: &str, 
    size: ImageSize
) -> Result<ImageData, PhotoError>;
```

### 批量获取图片

```rust
/// 批量获取多个图片的数据
/// 
/// # 参数
/// - `asset_ids`: 图片资源 ID 列表
/// - `options`: 图片请求选项
/// 
/// # 返回值
/// - `Ok(Vec<Result<ImageData, PhotoError>>)`: 每个图片的获取结果
/// 
/// # 注意
/// - 返回的结果顺序与输入的 ID 顺序一致
/// - 单个图片获取失败不会影响其他图片
pub async fn get_multiple_images(
    asset_ids: &[String], 
    options: ImageRequestOptions
) -> Vec<Result<ImageData, PhotoError>>;
```

## 实用工具 API

### 权限引导

```rust
/// 打开系统设置页面以便用户手动授权
/// 
/// # 返回值
/// - `Ok(())`: 成功打开设置页面
/// - `Err(PhotoError)`: 打开失败的错误信息
pub fn open_settings() -> Result<(), PhotoError>;
```

### 缓存管理

```rust
/// 清理图片缓存
/// 
/// # 返回值
/// - `Ok(usize)`: 清理的缓存大小（字节）
/// - `Err(PhotoError)`: 清理失败的错误信息
pub fn clear_image_cache() -> Result<usize, PhotoError>;

/// 获取当前缓存大小
/// 
/// # 返回值
/// - `Ok(usize)`: 缓存大小（字节）
/// - `Err(PhotoError)`: 获取失败的错误信息
pub fn get_cache_size() -> Result<usize, PhotoError>;
```

## 使用示例

### 基本使用流程

```rust
use photo_access::*;

#[tokio::main]
async fn main() -> Result<(), PhotoError> {
    // 1. 检查权限状态
    let status = check_permission_status()?;
    
    if status != PhotoPermissionStatus::Authorized {
        // 2. 请求权限
        let new_status = request_permission().await?;
        
        if new_status != PhotoPermissionStatus::Authorized {
            eprintln!("权限被拒绝，请到设置中手动授权");
            open_settings()?;
            return Ok(());
        }
    }
    
    // 3. 获取相册列表
    let query = PhotoQuery {
        media_type: Some(MediaType::Image),
        limit: 20,
        ..Default::default()
    };
    
    let assets = get_photo_assets(query).await?;
    println!("找到 {} 张图片", assets.len());
    
    // 4. 获取第一张图片的缩略图
    if let Some(first_asset) = assets.first() {
        let thumbnail = get_thumbnail(
            &first_asset.id,
            ImageSize { width: 200, height: 200 }
        ).await?;
        
        println!("缩略图大小: {} 字节", thumbnail.byte_size);
    }
    
    Ok(())
}
```

### 错误处理示例

```rust
async fn handle_photo_access() {
    match get_photo_assets(PhotoQuery::default()).await {
        Ok(assets) => {
            println!("成功获取 {} 个媒体资源", assets.len());
        }
        Err(PhotoError::PermissionDenied) => {
            eprintln!("权限被拒绝，请授权后重试");
            if let Err(e) = open_settings() {
                eprintln!("无法打开设置页面: {}", e);
            }
        }
        Err(PhotoError::NetworkError { message }) => {
            eprintln!("网络错误: {}", message);
        }
        Err(e) => {
            eprintln!("其他错误: {}", e);
        }
    }
}
```
