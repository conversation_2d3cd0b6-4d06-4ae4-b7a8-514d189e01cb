# 系统架构 - 系统相册访问功能

## 执行摘要

本架构设计基于 swift-bridge 框架，为 Rust 应用提供原生的系统相册访问能力。采用分层架构模式，通过 Swift 桥接层访问 Apple Photos 框架，实现类型安全、内存高效的跨语言互操作。架构重点关注权限管理、数据传输优化和错误处理的统一性。

## 架构概述

### 系统上下文
```mermaid
C4Context
    Person(developer, "Rust 开发者", "使用相册访问功能的开发者")
    System(photoAccess, "相册访问系统", "Rust 相册访问库")
    System_Ext(photos, "Apple Photos 框架", "系统相册服务")
    System_Ext(permissions, "权限系统", "iOS/macOS 权限管理")

    Rel(developer, photoAccess, "调用 API")
    Rel(photoAccess, photos, "访问相册数据")
    Rel(photoAccess, permissions, "请求权限")
```

### 容器架构图
```mermaid
C4Container
    Container(rustApp, "Rust 应用", "Rust", "用户应用程序")
    Container(rustLib, "Rust 库", "Rust", "相册访问核心逻辑")
    Container(swiftBridge, "Swift 桥接层", "Swift", "系统 API 封装")
    Container(photosFramework, "Photos 框架", "Swift/ObjC", "Apple 系统框架")

    Rel(rustApp, rustLib, "函数调用")
    Rel(rustLib, swiftBridge, "FFI 调用")
    Rel(swiftBridge, photosFramework, "原生 API 调用")
```

## 技术栈

### 核心技术
- **语言**: Rust (主要逻辑) + Swift (系统集成)
- **互操作框架**: swift-bridge 2.0+
- **系统框架**: Photos.framework, PhotosUI.framework
- **构建工具**: Cargo + Xcode

### 开发工具
- **IDE**: Xcode 14+ (Swift), VS Code/RustRover (Rust)
- **版本控制**: Git
- **包管理**: Cargo (Rust), Swift Package Manager
- **测试框架**: XCTest (Swift), Rust test (Rust)

### 平台支持
- **iOS**: 14.0+
- **macOS**: 11.0+
- **Rust**: 1.70+
- **Swift**: 5.7+

## 组件设计

### Rust 核心层 (rust_lib)
**目的**: 提供类型安全的 Rust API 接口
**技术**: Rust
**接口**:
- 输入: Rust 函数调用
- 输出: Result<T, PhotoError> 类型
**依赖**: swift_bridge 生成的绑定

**主要模块**:
```rust
// 权限管理模块
pub mod permissions;
// 相册访问模块  
pub mod album;
// 图片处理模块
pub mod image;
// 错误处理模块
pub mod error;
// 异步支持模块
pub mod async_support;
```

### Swift 桥接层 (swift_bridge)
**目的**: 封装 Apple Photos API 并提供 C 兼容接口
**技术**: Swift + swift-bridge 宏
**接口**:
- 输入: C 兼容的函数调用
- 输出: C 兼容的返回值
**依赖**: Photos.framework, Foundation.framework

**核心组件**:
```swift
// 权限管理器
class PhotoPermissionManager
// 相册数据访问器
class PhotoAlbumAccessor  
// 图片数据管理器
class PhotoImageManager
// 错误转换器
class PhotoErrorConverter
```

### Apple Photos 框架层
**目的**: 系统级相册访问服务
**技术**: Apple Photos.framework
**接口**: 原生 Swift/ObjC API
**功能**: 权限管理、相册数据、图片处理

## 数据架构

### 数据流设计
```mermaid
flowchart TD
    A[Rust 应用] --> B[Rust API 层]
    B --> C[类型转换层]
    C --> D[Swift 桥接层]
    D --> E[Photos 框架]
    E --> F[系统相册]
    
    F --> G[原始数据]
    G --> H[Swift 数据处理]
    H --> I[类型转换]
    I --> J[Rust 数据结构]
    J --> K[应用程序]
```

### 核心数据模型
```rust
// 权限状态
#[derive(Debug, Clone)]
pub enum PhotoPermissionStatus {
    NotDetermined,
    Restricted, 
    Denied,
    Authorized,
    Limited, // iOS 14+
}

// 媒体项目
#[derive(Debug, Clone)]
pub struct PhotoAsset {
    pub id: String,
    pub media_type: MediaType,
    pub creation_date: Option<DateTime<Utc>>,
    pub pixel_width: u32,
    pub pixel_height: u32,
    pub duration: Option<f64>, // 视频时长
}

// 图片数据
#[derive(Debug)]
pub struct ImageData {
    pub data: Vec<u8>,
    pub format: ImageFormat,
    pub size: ImageSize,
}

// 错误类型
#[derive(Debug, thiserror::Error)]
pub enum PhotoError {
    #[error("权限被拒绝")]
    PermissionDenied,
    #[error("资源不存在: {id}")]
    AssetNotFound { id: String },
    #[error("系统错误: {message}")]
    SystemError { message: String },
}
```

## 安全架构

### 权限管理策略
- **最小权限原则**: 只请求必要的相册访问权限
- **权限状态检查**: 每次操作前验证权限状态
- **用户引导**: 权限被拒绝时提供清晰的用户指导
- **隐私保护**: 不缓存敏感的用户数据

### 数据安全措施
- **内存安全**: 利用 Rust 所有权系统防止内存泄漏
- **类型安全**: 强类型系统防止数据类型错误
- **边界检查**: 所有数组和缓冲区访问都进行边界检查
- **错误处理**: 完整的错误处理避免程序崩溃

## 可扩展性策略

### 水平扩展
- **模块化设计**: 每个功能模块独立，便于扩展
- **插件架构**: 支持第三方扩展和自定义处理器
- **异步支持**: 支持并发操作提高吞吐量

### 性能优化
- **懒加载**: 按需加载图片数据和元数据
- **缓存策略**: 智能缓存常用数据
- **内存池**: 重用内存分配减少 GC 压力
- **批量操作**: 支持批量获取多个资源

## 部署架构

### 构建流程
```yaml
构建步骤:
  1. Rust 代码编译
  2. Swift 桥接代码生成
  3. Swift 代码编译
  4. 静态库链接
  5. 最终产物打包

产物类型:
  - Rust 静态库 (.a)
  - Swift 模块 (.swiftmodule)
  - C 头文件 (.h)
  - 文档和示例
```

### 集成方式
- **Cargo 依赖**: 通过 Cargo.toml 添加依赖
- **Xcode 项目**: 直接集成到 iOS/macOS 项目
- **Swift Package**: 作为 Swift Package 分发
- **CocoaPods**: 支持 CocoaPods 集成

## 监控与可观测性

### 性能监控
- **响应时间**: 监控 API 调用响应时间
- **内存使用**: 跟踪内存分配和释放
- **错误率**: 统计各类错误的发生频率
- **资源使用**: 监控 CPU 和 I/O 使用情况

### 日志策略
- **结构化日志**: 使用结构化格式记录操作日志
- **错误追踪**: 详细记录错误堆栈和上下文
- **性能日志**: 记录关键操作的性能数据
- **隐私保护**: 避免记录敏感用户数据

## 架构决策记录 (ADRs)

### ADR-001: 选择 swift-bridge 作为互操作框架
**状态**: 已接受
**上下文**: 需要在 Rust 和 Swift 之间进行高效互操作
**决策**: 使用 swift-bridge 框架而非手动 FFI
**后果**: 
- 优点: 类型安全、自动代码生成、维护成本低
- 缺点: 依赖第三方框架、学习成本
**替代方案**: 手动 FFI、cbindgen + 手动 Swift 绑定

### ADR-002: 采用分层架构模式
**状态**: 已接受  
**上下文**: 需要清晰的职责分离和可维护性
**决策**: 采用 Rust 核心层 + Swift 桥接层的分层架构
**后果**:
- 优点: 职责清晰、易于测试、可维护性高
- 缺点: 增加了一定的复杂性
**替代方案**: 单层架构、微服务架构

### ADR-003: 使用 Result<T, E> 错误处理模式
**状态**: 已接受
**上下文**: 需要统一的错误处理机制
**决策**: 使用 Rust 标准的 Result 类型处理错误
**后果**:
- 优点: 类型安全、强制错误处理、符合 Rust 惯例
- 缺点: 增加了 API 复杂度
**替代方案**: 异常机制、错误码模式
