name: test

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - uses: actions/checkout@v2

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Rust Version Info
        run: rustc --version && cargo --version

      - name: Cargo format
        run: cargo fmt --all -- --check

      - name: Run tests
        run: |
          RUSTFLAGS="-D warnings" cargo test -p swift-bridge \
            -p swift-bridge-build \
            -p swift-bridge-cli \
            -p swift-bridge-ir \
            -p swift-bridge-macro \
            -p swift-integration-tests

  swift-package-test:
    runs-on: macos-14
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v2

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Add rust targets
        run: rustup target add aarch64-apple-darwin x86_64-apple-darwin

      - name: Run swift package tests
        run: ./test-swift-packages.sh

  integration-test:
    runs-on: macos-14
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v2

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Add rust targets
        run: rustup target add aarch64-apple-darwin x86_64-apple-darwin

      - name: Run integration tests
        run: ./test-swift-rust-integration.sh

  build-examples:
    runs-on: macos-14
    timeout-minutes: 15

    steps:
      - uses: actions/checkout@v2

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Add rust targets
        run: rustup target add aarch64-apple-darwin x86_64-apple-darwin

      - name: Build codegen-visualizer example
        run: xcodebuild -project examples/codegen-visualizer/CodegenVisualizer/CodegenVisualizer.xcodeproj -scheme CodegenVisualizer

      - name: Build async function example
        run: ./examples/async-functions/build.sh

      - name: Build Rust binary calls Swift Package examaple
        run: cargo build -p rust-binary-calls-swift-package

      - name: Build without-a-bridge-module example
        run: cargo build -p without-a-bridge-module